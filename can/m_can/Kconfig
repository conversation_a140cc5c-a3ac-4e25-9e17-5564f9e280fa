# SPDX-License-Identifier: GPL-2.0-only
config CAN_M_CAN
	tristate "Bosch M_CAN support"
	---help---
	  Say Y here if you want support for Bosch M_CAN controller framework.
	  This is common support for devices that embed the Bosch M_CAN IP.

config CAN_M_CAN_PLATFORM
	tristate "Bosch M_CAN support for io-mapped devices"
	depends on HAS_IOMEM
	depends on CAN_M_CAN
	---help---
	  Say Y here if you want support for IO Mapped Bosch M_CAN controller.
	  This support is for devices that have the Bosch M_CAN controller
	  IP embedded into the device and the IP is IO Mapped to the processor.

config CAN_M_CAN_TCAN4X5X
	depends on CAN_M_CAN
	depends on SPI
	select REGMAP_SPI
	tristate "TCAN4X5X M_CAN device"
	---help---
	  Say Y here if you want support for Texas Instruments TCAN4x5x
	  M_CAN controller.  This device is a peripherial device that uses the
	  SPI bus for communication.
