# SPDX-License-Identifier: GPL-2.0-only
menuconfig CAN_CC770
	tristate "Bosch CC770 and Intel AN82527 devices"
	depends on HAS_IOMEM

if CAN_CC770

config CAN_CC770_ISA
	tristate "ISA Bus based legacy CC770 driver"
	---help---
	  This driver adds legacy support for CC770 and AN82527 chips
	  connected to the ISA bus using I/O port, memory mapped or
	  indirect access.

config CAN_CC770_PLATFORM
	tristate "Generic Platform Bus based CC770 driver"
	---help---
	  This driver adds support for the CC770 and AN82527 chips
	  connected to the "platform bus" (Linux abstraction for directly
	  to the processor attached devices).

endif
