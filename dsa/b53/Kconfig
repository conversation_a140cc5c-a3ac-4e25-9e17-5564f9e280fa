# SPDX-License-Identifier: GPL-2.0-only
menuconfig B53
	tristate "Broadcom BCM53xx managed switch support"
	depends on NET_DSA
	select NET_DSA_TAG_BRCM
	select NET_DSA_TAG_BRCM_PREPEND
	help
	  This driver adds support for Broadcom managed switch chips. It supports
	  BCM5325E, BCM5365, BCM539x, BCM53115 and BCM53125 as well as BCM63XX
	  integrated switches.

config B53_SPI_DRIVER
	tristate "B53 SPI connected switch driver"
	depends on B53 && SPI
	help
	  Select to enable support for registering switches configured through SPI.

config B53_MDIO_DRIVER
	tristate "B53 MDIO connected switch driver"
	depends on B53
	help
	  Select to enable support for registering switches configured through MDIO.

config B53_MMAP_DRIVER
	tristate "B53 MMAP connected switch driver"
	depends on B53 && HAS_IOMEM
	default BCM63XX || BMIPS_GENERIC
	help
	  Select to enable support for memory-mapped switches like the BCM63XX
	  integrated switches.

config B53_SRAB_DRIVER
	tristate "B53 SRAB connected switch driver"
	depends on B53 && HAS_IOMEM
	depends on B53_SERDES || !B53_SERDES
	default ARCH_BCM_IPROC
	help
	  Select to enable support for memory-mapped Switch Register Access
	  Bridge Registers (SRAB) like it is found on the BCM53010

config B53_SERDES
	tristate "B53 SerDes support"
	depends on B53
	default ARCH_BCM_NSP
	help
	  Select to enable support for SerDes on e.g: Northstar Plus SoCs.
