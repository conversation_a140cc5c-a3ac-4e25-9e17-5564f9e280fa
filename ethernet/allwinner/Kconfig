# SPDX-License-Identifier: GPL-2.0-only
#
# Allwinner device configuration
#

config NET_VENDOR_ALLWINNER
	bool "Allwinner devices"
	default y

	depends on ARCH_SUNXI
	---help---
	  If you have a network (Ethernet) card belonging to this
	  class, say Y here.

	  Note that the answer to this question doesn't directly
	  affect the kernel: saying N will just cause the configurator
	  to skip all the questions about Allwinner cards. If you say Y,
	  you will be asked for your specific card in the following
	  questions.

if NET_VENDOR_ALLWINNER

config SUN4I_EMAC
	tristate "Allwinner A10 EMAC support"
	depends on ARCH_SUNXI
	depends on OF
	select CRC32
	select MII
	select PH<PERSON><PERSON><PERSON>
	select MDIO_SUN4I
	---help---
	  Support for Allwinner A10 EMAC ethernet driver.

	  To compile this driver as a module, choose M here.  The module
	  will be called sun4i-emac.

endif # NET_VENDOR_ALLWINNER
