/* SPDX-License-Identifier: GPL-2.0 */
/* $Id: tg3.h,v 1.37.2.32 2002/03/11 12:18:18 davem Exp $
 * tg3.h: Definitions for Broadcom Tigon3 ethernet driver.
 *
 * Copyright (C) 2001, 2002, 2003, 2004 <PERSON> (<EMAIL>)
 * Copyright (C) 2001 <PERSON> (jgar<PERSON><EMAIL>)
 * Copyright (C) 2004 Sun Microsystems Inc.
 * Copyright (C) 2007-2016 Broadcom Corporation.
 * Copyright (C) 2016-2017 Broadcom Limited.
 * Copyright (C) 2018 Broadcom. All Rights Reserved. The term "Broadcom"
 * refers to Broadcom Inc. and/or its subsidiaries.
 */

#ifndef _T3_H
#define _T3_H

#define TG3_64BIT_REG_HIGH		0x00UL
#define TG3_64BIT_REG_LOW		0x04UL

/* Descriptor block info. */
#define TG3_BDINFO_HOST_ADDR		0x0UL /* 64-bit */
#define TG3_BDINFO_MAXLEN_FLAGS		0x8UL /* 32-bit */
#define  BDINFO_FLAGS_USE_EXT_RECV	 0x00000001 /* ext rx_buffer_desc */
#define  BDINFO_FLAGS_DISABLED		 0x00000002
#define  BDINFO_FLAGS_MAXLEN_MASK	 0xffff0000
#define  BDINFO_FLAGS_MAXLEN_SHIFT	 16
#define TG3_BDINFO_NIC_ADDR		0xcUL /* 32-bit */
#define TG3_BDINFO_SIZE			0x10UL

#define TG3_RX_STD_MAX_SIZE_5700	512
#define TG3_RX_STD_MAX_SIZE_5717	2048
#define TG3_RX_JMB_MAX_SIZE_5700	256
#define TG3_RX_JMB_MAX_SIZE_5717	1024
#define TG3_RX_RET_MAX_SIZE_5700	1024
#define TG3_RX_RET_MAX_SIZE_5705	512
#define TG3_RX_RET_MAX_SIZE_5717	4096

#define TG3_RSS_INDIR_TBL_SIZE		128

/* First 256 bytes are a mirror of PCI config space. */
#define TG3PCI_VENDOR			0x00000000
#define  TG3PCI_VENDOR_BROADCOM		 0x14e4
#define TG3PCI_DEVICE			0x00000002
#define  TG3PCI_DEVICE_TIGON3_1		 0x1644 /* BCM5700 */
#define  TG3PCI_DEVICE_TIGON3_2		 0x1645 /* BCM5701 */
#define  TG3PCI_DEVICE_TIGON3_3		 0x1646 /* BCM5702 */
#define  TG3PCI_DEVICE_TIGON3_4		 0x1647 /* BCM5703 */
#define  TG3PCI_DEVICE_TIGON3_5761S	 0x1688
#define  TG3PCI_DEVICE_TIGON3_5761SE	 0x1689
#define  TG3PCI_DEVICE_TIGON3_57780	 0x1692
#define  TG3PCI_DEVICE_TIGON3_5787M	 0x1693
#define  TG3PCI_DEVICE_TIGON3_57760	 0x1690
#define  TG3PCI_DEVICE_TIGON3_57790	 0x1694
#define  TG3PCI_DEVICE_TIGON3_57788	 0x1691
#define  TG3PCI_DEVICE_TIGON3_5785_G	 0x1699 /* GPHY */
#define  TG3PCI_DEVICE_TIGON3_5785_F	 0x16a0 /* 10/100 only */
#define  TG3PCI_DEVICE_TIGON3_5717	 0x1655
#define  TG3PCI_DEVICE_TIGON3_5717_C	 0x1665
#define  TG3PCI_DEVICE_TIGON3_5718	 0x1656
#define  TG3PCI_DEVICE_TIGON3_57781	 0x16b1
#define  TG3PCI_DEVICE_TIGON3_57785	 0x16b5
#define  TG3PCI_DEVICE_TIGON3_57761	 0x16b0
#define  TG3PCI_DEVICE_TIGON3_57765	 0x16b4
#define  TG3PCI_DEVICE_TIGON3_57791	 0x16b2
#define  TG3PCI_DEVICE_TIGON3_57795	 0x16b6
#define  TG3PCI_DEVICE_TIGON3_5719	 0x1657
#define  TG3PCI_DEVICE_TIGON3_5720	 0x165f
#define  TG3PCI_DEVICE_TIGON3_57762	 0x1682
#define  TG3PCI_DEVICE_TIGON3_57766	 0x1686
#define  TG3PCI_DEVICE_TIGON3_57786	 0x16b3
#define  TG3PCI_DEVICE_TIGON3_57782	 0x16b7
#define  TG3PCI_DEVICE_TIGON3_5762	 0x1687
#define  TG3PCI_DEVICE_TIGON3_5725	 0x1643
#define  TG3PCI_DEVICE_TIGON3_5727	 0x16f3
#define  TG3PCI_DEVICE_TIGON3_57764	 0x1642
#define  TG3PCI_DEVICE_TIGON3_57767	 0x1683
#define  TG3PCI_DEVICE_TIGON3_57787	 0x1641
/* 0x04 --> 0x2c unused */
#define TG3PCI_SUBVENDOR_ID_BROADCOM		PCI_VENDOR_ID_BROADCOM
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95700A6	0x1644
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95701A5	0x0001
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95700T6	0x0002
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95700A9	0x0003
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95701T1	0x0005
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95701T8	0x0006
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95701A7	0x0007
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95701A10	0x0008
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95701A12	0x8008
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95703AX1	0x0009
#define TG3PCI_SUBDEVICE_ID_BROADCOM_95703AX2	0x8009
#define TG3PCI_SUBVENDOR_ID_3COM		PCI_VENDOR_ID_3COM
#define TG3PCI_SUBDEVICE_ID_3COM_3C996T		0x1000
#define TG3PCI_SUBDEVICE_ID_3COM_3C996BT	0x1006
#define TG3PCI_SUBDEVICE_ID_3COM_3C996SX	0x1004
#define TG3PCI_SUBDEVICE_ID_3COM_3C1000T	0x1007
#define TG3PCI_SUBDEVICE_ID_3COM_3C940BR01	0x1008
#define TG3PCI_SUBVENDOR_ID_DELL		PCI_VENDOR_ID_DELL
#define TG3PCI_SUBDEVICE_ID_DELL_VIPER		0x00d1
#define TG3PCI_SUBDEVICE_ID_DELL_JAGUAR		0x0106
#define TG3PCI_SUBDEVICE_ID_DELL_MERLOT		0x0109
#define TG3PCI_SUBDEVICE_ID_DELL_SLIM_MERLOT	0x010a
#define TG3PCI_SUBDEVICE_ID_DELL_5762		0x07f0
#define TG3PCI_SUBVENDOR_ID_COMPAQ		PCI_VENDOR_ID_COMPAQ
#define TG3PCI_SUBDEVICE_ID_COMPAQ_BANSHEE	0x007c
#define TG3PCI_SUBDEVICE_ID_COMPAQ_BANSHEE_2	0x009a
#define TG3PCI_SUBDEVICE_ID_COMPAQ_CHANGELING	0x007d
#define TG3PCI_SUBDEVICE_ID_COMPAQ_NC7780	0x0085
#define TG3PCI_SUBDEVICE_ID_COMPAQ_NC7780_2	0x0099
#define TG3PCI_SUBVENDOR_ID_IBM			PCI_VENDOR_ID_IBM
#define TG3PCI_SUBDEVICE_ID_IBM_5703SAX2	0x0281
#define TG3PCI_SUBDEVICE_ID_ACER_57780_A	0x0601
#define TG3PCI_SUBDEVICE_ID_ACER_57780_B	0x0612
#define TG3PCI_SUBDEVICE_ID_LENOVO_5787M	0x3056

/* 0x30 --> 0x64 unused */
#define TG3PCI_MSI_DATA			0x00000064
/* 0x66 --> 0x68 unused */
#define TG3PCI_MISC_HOST_CTRL		0x00000068
#define  MISC_HOST_CTRL_CLEAR_INT	 0x00000001
#define  MISC_HOST_CTRL_MASK_PCI_INT	 0x00000002
#define  MISC_HOST_CTRL_BYTE_SWAP	 0x00000004
#define  MISC_HOST_CTRL_WORD_SWAP	 0x00000008
#define  MISC_HOST_CTRL_PCISTATE_RW	 0x00000010
#define  MISC_HOST_CTRL_CLKREG_RW	 0x00000020
#define  MISC_HOST_CTRL_REGWORD_SWAP	 0x00000040
#define  MISC_HOST_CTRL_INDIR_ACCESS	 0x00000080
#define  MISC_HOST_CTRL_IRQ_MASK_MODE	 0x00000100
#define  MISC_HOST_CTRL_TAGGED_STATUS	 0x00000200
#define  MISC_HOST_CTRL_CHIPREV		 0xffff0000
#define  MISC_HOST_CTRL_CHIPREV_SHIFT	 16

#define  CHIPREV_ID_5700_A0		 0x7000
#define  CHIPREV_ID_5700_A1		 0x7001
#define  CHIPREV_ID_5700_B0		 0x7100
#define  CHIPREV_ID_5700_B1		 0x7101
#define  CHIPREV_ID_5700_B3		 0x7102
#define  CHIPREV_ID_5700_ALTIMA		 0x7104
#define  CHIPREV_ID_5700_C0		 0x7200
#define  CHIPREV_ID_5701_A0		 0x0000
#define  CHIPREV_ID_5701_B0		 0x0100
#define  CHIPREV_ID_5701_B2		 0x0102
#define  CHIPREV_ID_5701_B5		 0x0105
#define  CHIPREV_ID_5703_A0		 0x1000
#define  CHIPREV_ID_5703_A1		 0x1001
#define  CHIPREV_ID_5703_A2		 0x1002
#define  CHIPREV_ID_5703_A3		 0x1003
#define  CHIPREV_ID_5704_A0		 0x2000
#define  CHIPREV_ID_5704_A1		 0x2001
#define  CHIPREV_ID_5704_A2		 0x2002
#define  CHIPREV_ID_5704_A3		 0x2003
#define  CHIPREV_ID_5705_A0		 0x3000
#define  CHIPREV_ID_5705_A1		 0x3001
#define  CHIPREV_ID_5705_A2		 0x3002
#define  CHIPREV_ID_5705_A3		 0x3003
#define  CHIPREV_ID_5750_A0		 0x4000
#define  CHIPREV_ID_5750_A1		 0x4001
#define  CHIPREV_ID_5750_A3		 0x4003
#define  CHIPREV_ID_5750_C2		 0x4202
#define  CHIPREV_ID_5752_A0_HW		 0x5000
#define  CHIPREV_ID_5752_A0		 0x6000
#define  CHIPREV_ID_5752_A1		 0x6001
#define  CHIPREV_ID_5714_A2		 0x9002
#define  CHIPREV_ID_5906_A1		 0xc001
#define  CHIPREV_ID_57780_A0		 0x57780000
#define  CHIPREV_ID_57780_A1		 0x57780001
#define  CHIPREV_ID_5717_A0		 0x05717000
#define  CHIPREV_ID_5717_C0		 0x05717200
#define  CHIPREV_ID_57765_A0		 0x57785000
#define  CHIPREV_ID_5719_A0		 0x05719000
#define  CHIPREV_ID_5720_A0		 0x05720000
#define  CHIPREV_ID_5762_A0		 0x05762000

#define   ASIC_REV_5700			 0x07
#define   ASIC_REV_5701			 0x00
#define   ASIC_REV_5703			 0x01
#define   ASIC_REV_5704			 0x02
#define   ASIC_REV_5705			 0x03
#define   ASIC_REV_5750			 0x04
#define   ASIC_REV_5752			 0x06
#define   ASIC_REV_5780			 0x08
#define   ASIC_REV_5714			 0x09
#define   ASIC_REV_5755			 0x0a
#define   ASIC_REV_5787			 0x0b
#define   ASIC_REV_5906			 0x0c
#define   ASIC_REV_USE_PROD_ID_REG	 0x0f
#define   ASIC_REV_5784			 0x5784
#define   ASIC_REV_5761			 0x5761
#define   ASIC_REV_5785			 0x5785
#define   ASIC_REV_57780		 0x57780
#define   ASIC_REV_5717			 0x5717
#define   ASIC_REV_57765		 0x57785
#define   ASIC_REV_5719			 0x5719
#define   ASIC_REV_5720			 0x5720
#define   ASIC_REV_57766		 0x57766
#define   ASIC_REV_5762			 0x5762
#define   CHIPREV_5700_AX		 0x70
#define   CHIPREV_5700_BX		 0x71
#define   CHIPREV_5700_CX		 0x72
#define   CHIPREV_5701_AX		 0x00
#define   CHIPREV_5703_AX		 0x10
#define   CHIPREV_5704_AX		 0x20
#define   CHIPREV_5704_BX		 0x21
#define   CHIPREV_5750_AX		 0x40
#define   CHIPREV_5750_BX		 0x41
#define   CHIPREV_5784_AX		 0x57840
#define   CHIPREV_5761_AX		 0x57610
#define   CHIPREV_57765_AX		 0x577650
#define   METAL_REV_A0			 0x00
#define   METAL_REV_A1			 0x01
#define   METAL_REV_B0			 0x00
#define   METAL_REV_B1			 0x01
#define   METAL_REV_B2			 0x02
#define TG3PCI_DMA_RW_CTRL		0x0000006c
#define  DMA_RWCTRL_DIS_CACHE_ALIGNMENT  0x00000001
#define  DMA_RWCTRL_TAGGED_STAT_WA	 0x00000080
#define  DMA_RWCTRL_CRDRDR_RDMA_MRRS_MSK 0x00000380
#define  DMA_RWCTRL_READ_BNDRY_MASK	 0x00000700
#define  DMA_RWCTRL_READ_BNDRY_DISAB	 0x00000000
#define  DMA_RWCTRL_READ_BNDRY_16	 0x00000100
#define  DMA_RWCTRL_READ_BNDRY_128_PCIX	 0x00000100
#define  DMA_RWCTRL_READ_BNDRY_32	 0x00000200
#define  DMA_RWCTRL_READ_BNDRY_256_PCIX	 0x00000200
#define  DMA_RWCTRL_READ_BNDRY_64	 0x00000300
#define  DMA_RWCTRL_READ_BNDRY_384_PCIX	 0x00000300
#define  DMA_RWCTRL_READ_BNDRY_128	 0x00000400
#define  DMA_RWCTRL_READ_BNDRY_256	 0x00000500
#define  DMA_RWCTRL_READ_BNDRY_512	 0x00000600
#define  DMA_RWCTRL_READ_BNDRY_1024	 0x00000700
#define  DMA_RWCTRL_WRITE_BNDRY_MASK	 0x00003800
#define  DMA_RWCTRL_WRITE_BNDRY_DISAB	 0x00000000
#define  DMA_RWCTRL_WRITE_BNDRY_16	 0x00000800
#define  DMA_RWCTRL_WRITE_BNDRY_128_PCIX 0x00000800
#define  DMA_RWCTRL_WRITE_BNDRY_32	 0x00001000
#define  DMA_RWCTRL_WRITE_BNDRY_256_PCIX 0x00001000
#define  DMA_RWCTRL_WRITE_BNDRY_64	 0x00001800
#define  DMA_RWCTRL_WRITE_BNDRY_384_PCIX 0x00001800
#define  DMA_RWCTRL_WRITE_BNDRY_128	 0x00002000
#define  DMA_RWCTRL_WRITE_BNDRY_256	 0x00002800
#define  DMA_RWCTRL_WRITE_BNDRY_512	 0x00003000
#define  DMA_RWCTRL_WRITE_BNDRY_1024	 0x00003800
#define  DMA_RWCTRL_ONE_DMA		 0x00004000
#define  DMA_RWCTRL_READ_WATER		 0x00070000
#define  DMA_RWCTRL_READ_WATER_SHIFT	 16
#define  DMA_RWCTRL_WRITE_WATER		 0x00380000
#define  DMA_RWCTRL_WRITE_WATER_SHIFT	 19
#define  DMA_RWCTRL_USE_MEM_READ_MULT	 0x00400000
#define  DMA_RWCTRL_ASSERT_ALL_BE	 0x00800000
#define  DMA_RWCTRL_PCI_READ_CMD	 0x0f000000
#define  DMA_RWCTRL_PCI_READ_CMD_SHIFT	 24
#define  DMA_RWCTRL_PCI_WRITE_CMD	 0xf0000000
#define  DMA_RWCTRL_PCI_WRITE_CMD_SHIFT	 28
#define  DMA_RWCTRL_WRITE_BNDRY_64_PCIE	 0x10000000
#define  DMA_RWCTRL_WRITE_BNDRY_128_PCIE 0x30000000
#define  DMA_RWCTRL_WRITE_BNDRY_DISAB_PCIE 0x70000000
#define TG3PCI_PCISTATE			0x00000070
#define  PCISTATE_FORCE_RESET		 0x00000001
#define  PCISTATE_INT_NOT_ACTIVE	 0x00000002
#define  PCISTATE_CONV_PCI_MODE		 0x00000004
#define  PCISTATE_BUS_SPEED_HIGH	 0x00000008
#define  PCISTATE_BUS_32BIT		 0x00000010
#define  PCISTATE_ROM_ENABLE		 0x00000020
#define  PCISTATE_ROM_RETRY_ENABLE	 0x00000040
#define  PCISTATE_FLAT_VIEW		 0x00000100
#define  PCISTATE_RETRY_SAME_DMA	 0x00002000
#define  PCISTATE_ALLOW_APE_CTLSPC_WR	 0x00010000
#define  PCISTATE_ALLOW_APE_SHMEM_WR	 0x00020000
#define  PCISTATE_ALLOW_APE_PSPACE_WR	 0x00040000
#define TG3PCI_CLOCK_CTRL		0x00000074
#define  CLOCK_CTRL_CORECLK_DISABLE	 0x00000200
#define  CLOCK_CTRL_RXCLK_DISABLE	 0x00000400
#define  CLOCK_CTRL_TXCLK_DISABLE	 0x00000800
#define  CLOCK_CTRL_ALTCLK		 0x00001000
#define  CLOCK_CTRL_PWRDOWN_PLL133	 0x00008000
#define  CLOCK_CTRL_44MHZ_CORE		 0x00040000
#define  CLOCK_CTRL_625_CORE		 0x00100000
#define  CLOCK_CTRL_FORCE_CLKRUN	 0x00200000
#define  CLOCK_CTRL_CLKRUN_OENABLE	 0x00400000
#define  CLOCK_CTRL_DELAY_PCI_GRANT	 0x80000000
#define TG3PCI_REG_BASE_ADDR		0x00000078
#define TG3PCI_MEM_WIN_BASE_ADDR	0x0000007c
#define TG3PCI_REG_DATA			0x00000080
#define TG3PCI_MEM_WIN_DATA		0x00000084
#define TG3PCI_MISC_LOCAL_CTRL		0x00000090
/* 0x94 --> 0x98 unused */
#define TG3PCI_STD_RING_PROD_IDX	0x00000098 /* 64-bit */
#define TG3PCI_RCV_RET_RING_CON_IDX	0x000000a0 /* 64-bit */
/* 0xa8 --> 0xb8 unused */
#define TG3PCI_DEV_STATUS_CTRL		0x000000b4
#define  MAX_READ_REQ_SIZE_2048		 0x00004000
#define  MAX_READ_REQ_MASK		 0x00007000
#define TG3PCI_DUAL_MAC_CTRL		0x000000b8
#define  DUAL_MAC_CTRL_CH_MASK		 0x00000003
#define  DUAL_MAC_CTRL_ID		 0x00000004
#define TG3PCI_PRODID_ASICREV		0x000000bc
#define  PROD_ID_ASIC_REV_MASK		 0x0fffffff
/* 0xc0 --> 0xf4 unused */

#define TG3PCI_GEN2_PRODID_ASICREV	0x000000f4
#define TG3PCI_GEN15_PRODID_ASICREV	0x000000fc
/* 0xf8 --> 0x200 unused */

#define TG3_CORR_ERR_STAT		0x00000110
#define  TG3_CORR_ERR_STAT_CLEAR	0xffffffff
/* 0x114 --> 0x200 unused */

/* Mailbox registers */
#define MAILBOX_INTERRUPT_0		0x00000200 /* 64-bit */
#define MAILBOX_INTERRUPT_1		0x00000208 /* 64-bit */
#define MAILBOX_INTERRUPT_2		0x00000210 /* 64-bit */
#define MAILBOX_INTERRUPT_3		0x00000218 /* 64-bit */
#define MAILBOX_GENERAL_0		0x00000220 /* 64-bit */
#define MAILBOX_GENERAL_1		0x00000228 /* 64-bit */
#define MAILBOX_GENERAL_2		0x00000230 /* 64-bit */
#define MAILBOX_GENERAL_3		0x00000238 /* 64-bit */
#define MAILBOX_GENERAL_4		0x00000240 /* 64-bit */
#define MAILBOX_GENERAL_5		0x00000248 /* 64-bit */
#define MAILBOX_GENERAL_6		0x00000250 /* 64-bit */
#define MAILBOX_GENERAL_7		0x00000258 /* 64-bit */
#define MAILBOX_RELOAD_STAT		0x00000260 /* 64-bit */
#define MAILBOX_RCV_STD_PROD_IDX	0x00000268 /* 64-bit */
#define TG3_RX_STD_PROD_IDX_REG		(MAILBOX_RCV_STD_PROD_IDX + \
					 TG3_64BIT_REG_LOW)
#define MAILBOX_RCV_JUMBO_PROD_IDX	0x00000270 /* 64-bit */
#define TG3_RX_JMB_PROD_IDX_REG		(MAILBOX_RCV_JUMBO_PROD_IDX + \
					 TG3_64BIT_REG_LOW)
#define MAILBOX_RCV_MINI_PROD_IDX	0x00000278 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_0	0x00000280 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_1	0x00000288 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_2	0x00000290 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_3	0x00000298 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_4	0x000002a0 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_5	0x000002a8 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_6	0x000002b0 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_7	0x000002b8 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_8	0x000002c0 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_9	0x000002c8 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_10	0x000002d0 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_11	0x000002d8 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_12	0x000002e0 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_13	0x000002e8 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_14	0x000002f0 /* 64-bit */
#define MAILBOX_RCVRET_CON_IDX_15	0x000002f8 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_0	0x00000300 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_1	0x00000308 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_2	0x00000310 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_3	0x00000318 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_4	0x00000320 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_5	0x00000328 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_6	0x00000330 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_7	0x00000338 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_8	0x00000340 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_9	0x00000348 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_10	0x00000350 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_11	0x00000358 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_12	0x00000360 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_13	0x00000368 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_14	0x00000370 /* 64-bit */
#define MAILBOX_SNDHOST_PROD_IDX_15	0x00000378 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_0	0x00000380 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_1	0x00000388 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_2	0x00000390 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_3	0x00000398 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_4	0x000003a0 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_5	0x000003a8 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_6	0x000003b0 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_7	0x000003b8 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_8	0x000003c0 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_9	0x000003c8 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_10	0x000003d0 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_11	0x000003d8 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_12	0x000003e0 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_13	0x000003e8 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_14	0x000003f0 /* 64-bit */
#define MAILBOX_SNDNIC_PROD_IDX_15	0x000003f8 /* 64-bit */

/* MAC control registers */
#define MAC_MODE			0x00000400
#define  MAC_MODE_RESET			 0x00000001
#define  MAC_MODE_HALF_DUPLEX		 0x00000002
#define  MAC_MODE_PORT_MODE_MASK	 0x0000000c
#define  MAC_MODE_PORT_MODE_TBI		 0x0000000c
#define  MAC_MODE_PORT_MODE_GMII	 0x00000008
#define  MAC_MODE_PORT_MODE_MII		 0x00000004
#define  MAC_MODE_PORT_MODE_NONE	 0x00000000
#define  MAC_MODE_PORT_INT_LPBACK	 0x00000010
#define  MAC_MODE_TAGGED_MAC_CTRL	 0x00000080
#define  MAC_MODE_TX_BURSTING		 0x00000100
#define  MAC_MODE_MAX_DEFER		 0x00000200
#define  MAC_MODE_LINK_POLARITY		 0x00000400
#define  MAC_MODE_RXSTAT_ENABLE		 0x00000800
#define  MAC_MODE_RXSTAT_CLEAR		 0x00001000
#define  MAC_MODE_RXSTAT_FLUSH		 0x00002000
#define  MAC_MODE_TXSTAT_ENABLE		 0x00004000
#define  MAC_MODE_TXSTAT_CLEAR		 0x00008000
#define  MAC_MODE_TXSTAT_FLUSH		 0x00010000
#define  MAC_MODE_SEND_CONFIGS		 0x00020000
#define  MAC_MODE_MAGIC_PKT_ENABLE	 0x00040000
#define  MAC_MODE_ACPI_ENABLE		 0x00080000
#define  MAC_MODE_MIP_ENABLE		 0x00100000
#define  MAC_MODE_TDE_ENABLE		 0x00200000
#define  MAC_MODE_RDE_ENABLE		 0x00400000
#define  MAC_MODE_FHDE_ENABLE		 0x00800000
#define  MAC_MODE_KEEP_FRAME_IN_WOL	 0x01000000
#define  MAC_MODE_APE_RX_EN		 0x08000000
#define  MAC_MODE_APE_TX_EN		 0x10000000
#define MAC_STATUS			0x00000404
#define  MAC_STATUS_PCS_SYNCED		 0x00000001
#define  MAC_STATUS_SIGNAL_DET		 0x00000002
#define  MAC_STATUS_RCVD_CFG		 0x00000004
#define  MAC_STATUS_CFG_CHANGED		 0x00000008
#define  MAC_STATUS_SYNC_CHANGED	 0x00000010
#define  MAC_STATUS_PORT_DEC_ERR	 0x00000400
#define  MAC_STATUS_LNKSTATE_CHANGED	 0x00001000
#define  MAC_STATUS_MI_COMPLETION	 0x00400000
#define  MAC_STATUS_MI_INTERRUPT	 0x00800000
#define  MAC_STATUS_AP_ERROR		 0x01000000
#define  MAC_STATUS_ODI_ERROR		 0x02000000
#define  MAC_STATUS_RXSTAT_OVERRUN	 0x04000000
#define  MAC_STATUS_TXSTAT_OVERRUN	 0x08000000
#define MAC_EVENT			0x00000408
#define  MAC_EVENT_PORT_DECODE_ERR	 0x00000400
#define  MAC_EVENT_LNKSTATE_CHANGED	 0x00001000
#define  MAC_EVENT_MI_COMPLETION	 0x00400000
#define  MAC_EVENT_MI_INTERRUPT		 0x00800000
#define  MAC_EVENT_AP_ERROR		 0x01000000
#define  MAC_EVENT_ODI_ERROR		 0x02000000
#define  MAC_EVENT_RXSTAT_OVERRUN	 0x04000000
#define  MAC_EVENT_TXSTAT_OVERRUN	 0x08000000
#define MAC_LED_CTRL			0x0000040c
#define  LED_CTRL_LNKLED_OVERRIDE	 0x00000001
#define  LED_CTRL_1000MBPS_ON		 0x00000002
#define  LED_CTRL_100MBPS_ON		 0x00000004
#define  LED_CTRL_10MBPS_ON		 0x00000008
#define  LED_CTRL_TRAFFIC_OVERRIDE	 0x00000010
#define  LED_CTRL_TRAFFIC_BLINK		 0x00000020
#define  LED_CTRL_TRAFFIC_LED		 0x00000040
#define  LED_CTRL_1000MBPS_STATUS	 0x00000080
#define  LED_CTRL_100MBPS_STATUS	 0x00000100
#define  LED_CTRL_10MBPS_STATUS		 0x00000200
#define  LED_CTRL_TRAFFIC_STATUS	 0x00000400
#define  LED_CTRL_MODE_MAC		 0x00000000
#define  LED_CTRL_MODE_PHY_1		 0x00000800
#define  LED_CTRL_MODE_PHY_2		 0x00001000
#define  LED_CTRL_MODE_SHASTA_MAC	 0x00002000
#define  LED_CTRL_MODE_SHARED		 0x00004000
#define  LED_CTRL_MODE_COMBO		 0x00008000
#define  LED_CTRL_BLINK_RATE_MASK	 0x7ff80000
#define  LED_CTRL_BLINK_RATE_SHIFT	 19
#define  LED_CTRL_BLINK_PER_OVERRIDE	 0x00080000
#define  LED_CTRL_BLINK_RATE_OVERRIDE	 0x80000000
#define MAC_ADDR_0_HIGH			0x00000410 /* upper 2 bytes */
#define MAC_ADDR_0_LOW			0x00000414 /* lower 4 bytes */
#define MAC_ADDR_1_HIGH			0x00000418 /* upper 2 bytes */
#define MAC_ADDR_1_LOW			0x0000041c /* lower 4 bytes */
#define MAC_ADDR_2_HIGH			0x00000420 /* upper 2 bytes */
#define MAC_ADDR_2_LOW			0x00000424 /* lower 4 bytes */
#define MAC_ADDR_3_HIGH			0x00000428 /* upper 2 bytes */
#define MAC_ADDR_3_LOW			0x0000042c /* lower 4 bytes */
#define MAC_ACPI_MBUF_PTR		0x00000430
#define MAC_ACPI_LEN_OFFSET		0x00000434
#define  ACPI_LENOFF_LEN_MASK		 0x0000ffff
#define  ACPI_LENOFF_LEN_SHIFT		 0
#define  ACPI_LENOFF_OFF_MASK		 0x0fff0000
#define  ACPI_LENOFF_OFF_SHIFT		 16
#define MAC_TX_BACKOFF_SEED		0x00000438
#define  TX_BACKOFF_SEED_MASK		 0x000003ff
#define MAC_RX_MTU_SIZE			0x0000043c
#define  RX_MTU_SIZE_MASK		 0x0000ffff
#define MAC_PCS_TEST			0x00000440
#define  PCS_TEST_PATTERN_MASK		 0x000fffff
#define  PCS_TEST_PATTERN_SHIFT		 0
#define  PCS_TEST_ENABLE		 0x00100000
#define MAC_TX_AUTO_NEG			0x00000444
#define  TX_AUTO_NEG_MASK		 0x0000ffff
#define  TX_AUTO_NEG_SHIFT		 0
#define MAC_RX_AUTO_NEG			0x00000448
#define  RX_AUTO_NEG_MASK		 0x0000ffff
#define  RX_AUTO_NEG_SHIFT		 0
#define MAC_MI_COM			0x0000044c
#define  MI_COM_CMD_MASK		 0x0c000000
#define  MI_COM_CMD_WRITE		 0x04000000
#define  MI_COM_CMD_READ		 0x08000000
#define  MI_COM_READ_FAILED		 0x10000000
#define  MI_COM_START			 0x20000000
#define  MI_COM_BUSY			 0x20000000
#define  MI_COM_PHY_ADDR_MASK		 0x03e00000
#define  MI_COM_PHY_ADDR_SHIFT		 21
#define  MI_COM_REG_ADDR_MASK		 0x001f0000
#define  MI_COM_REG_ADDR_SHIFT		 16
#define  MI_COM_DATA_MASK		 0x0000ffff
#define MAC_MI_STAT			0x00000450
#define  MAC_MI_STAT_LNKSTAT_ATTN_ENAB	 0x00000001
#define  MAC_MI_STAT_10MBPS_MODE	 0x00000002
#define MAC_MI_MODE			0x00000454
#define  MAC_MI_MODE_CLK_10MHZ		 0x00000001
#define  MAC_MI_MODE_SHORT_PREAMBLE	 0x00000002
#define  MAC_MI_MODE_AUTO_POLL		 0x00000010
#define  MAC_MI_MODE_500KHZ_CONST	 0x00008000
#define  MAC_MI_MODE_BASE		 0x000c0000 /* XXX magic values XXX */
#define MAC_AUTO_POLL_STATUS		0x00000458
#define  MAC_AUTO_POLL_ERROR		 0x00000001
#define MAC_TX_MODE			0x0000045c
#define  TX_MODE_RESET			 0x00000001
#define  TX_MODE_ENABLE			 0x00000002
#define  TX_MODE_FLOW_CTRL_ENABLE	 0x00000010
#define  TX_MODE_BIG_BCKOFF_ENABLE	 0x00000020
#define  TX_MODE_LONG_PAUSE_ENABLE	 0x00000040
#define  TX_MODE_MBUF_LOCKUP_FIX	 0x00000100
#define  TX_MODE_JMB_FRM_LEN		 0x00400000
#define  TX_MODE_CNT_DN_MODE		 0x00800000
#define MAC_TX_STATUS			0x00000460
#define  TX_STATUS_XOFFED		 0x00000001
#define  TX_STATUS_SENT_XOFF		 0x00000002
#define  TX_STATUS_SENT_XON		 0x00000004
#define  TX_STATUS_LINK_UP		 0x00000008
#define  TX_STATUS_ODI_UNDERRUN		 0x00000010
#define  TX_STATUS_ODI_OVERRUN		 0x00000020
#define MAC_TX_LENGTHS			0x00000464
#define  TX_LENGTHS_SLOT_TIME_MASK	 0x000000ff
#define  TX_LENGTHS_SLOT_TIME_SHIFT	 0
#define  TX_LENGTHS_IPG_MASK		 0x00000f00
#define  TX_LENGTHS_IPG_SHIFT		 8
#define  TX_LENGTHS_IPG_CRS_MASK	 0x00003000
#define  TX_LENGTHS_IPG_CRS_SHIFT	 12
#define  TX_LENGTHS_JMB_FRM_LEN_MSK	 0x00ff0000
#define  TX_LENGTHS_CNT_DWN_VAL_MSK	 0xff000000
#define MAC_RX_MODE			0x00000468
#define  RX_MODE_RESET			 0x00000001
#define  RX_MODE_ENABLE			 0x00000002
#define  RX_MODE_FLOW_CTRL_ENABLE	 0x00000004
#define  RX_MODE_KEEP_MAC_CTRL		 0x00000008
#define  RX_MODE_KEEP_PAUSE		 0x00000010
#define  RX_MODE_ACCEPT_OVERSIZED	 0x00000020
#define  RX_MODE_ACCEPT_RUNTS		 0x00000040
#define  RX_MODE_LEN_CHECK		 0x00000080
#define  RX_MODE_PROMISC		 0x00000100
#define  RX_MODE_NO_CRC_CHECK		 0x00000200
#define  RX_MODE_KEEP_VLAN_TAG		 0x00000400
#define  RX_MODE_RSS_IPV4_HASH_EN	 0x00010000
#define  RX_MODE_RSS_TCP_IPV4_HASH_EN	 0x00020000
#define  RX_MODE_RSS_IPV6_HASH_EN	 0x00040000
#define  RX_MODE_RSS_TCP_IPV6_HASH_EN	 0x00080000
#define  RX_MODE_RSS_ITBL_HASH_BITS_7	 0x00700000
#define  RX_MODE_RSS_ENABLE		 0x00800000
#define  RX_MODE_IPV6_CSUM_ENABLE	 0x01000000
#define  RX_MODE_IPV4_FRAG_FIX		 0x02000000
#define MAC_RX_STATUS			0x0000046c
#define  RX_STATUS_REMOTE_TX_XOFFED	 0x00000001
#define  RX_STATUS_XOFF_RCVD		 0x00000002
#define  RX_STATUS_XON_RCVD		 0x00000004
#define MAC_HASH_REG_0			0x00000470
#define MAC_HASH_REG_1			0x00000474
#define MAC_HASH_REG_2			0x00000478
#define MAC_HASH_REG_3			0x0000047c
#define MAC_RCV_RULE_0			0x00000480
#define MAC_RCV_VALUE_0			0x00000484
#define MAC_RCV_RULE_1			0x00000488
#define MAC_RCV_VALUE_1			0x0000048c
#define MAC_RCV_RULE_2			0x00000490
#define MAC_RCV_VALUE_2			0x00000494
#define MAC_RCV_RULE_3			0x00000498
#define MAC_RCV_VALUE_3			0x0000049c
#define MAC_RCV_RULE_4			0x000004a0
#define MAC_RCV_VALUE_4			0x000004a4
#define MAC_RCV_RULE_5			0x000004a8
#define MAC_RCV_VALUE_5			0x000004ac
#define MAC_RCV_RULE_6			0x000004b0
#define MAC_RCV_VALUE_6			0x000004b4
#define MAC_RCV_RULE_7			0x000004b8
#define MAC_RCV_VALUE_7			0x000004bc
#define MAC_RCV_RULE_8			0x000004c0
#define MAC_RCV_VALUE_8			0x000004c4
#define MAC_RCV_RULE_9			0x000004c8
#define MAC_RCV_VALUE_9			0x000004cc
#define MAC_RCV_RULE_10			0x000004d0
#define MAC_RCV_VALUE_10		0x000004d4
#define MAC_RCV_RULE_11			0x000004d8
#define MAC_RCV_VALUE_11		0x000004dc
#define MAC_RCV_RULE_12			0x000004e0
#define MAC_RCV_VALUE_12		0x000004e4
#define MAC_RCV_RULE_13			0x000004e8
#define MAC_RCV_VALUE_13		0x000004ec
#define MAC_RCV_RULE_14			0x000004f0
#define MAC_RCV_VALUE_14		0x000004f4
#define MAC_RCV_RULE_15			0x000004f8
#define MAC_RCV_VALUE_15		0x000004fc
#define  RCV_RULE_DISABLE_MASK		 0x7fffffff
#define MAC_RCV_RULE_CFG		0x00000500
#define  RCV_RULE_CFG_DEFAULT_CLASS	0x00000008
#define MAC_LOW_WMARK_MAX_RX_FRAME	0x00000504
/* 0x508 --> 0x520 unused */
#define MAC_HASHREGU_0			0x00000520
#define MAC_HASHREGU_1			0x00000524
#define MAC_HASHREGU_2			0x00000528
#define MAC_HASHREGU_3			0x0000052c
#define MAC_EXTADDR_0_HIGH		0x00000530
#define MAC_EXTADDR_0_LOW		0x00000534
#define MAC_EXTADDR_1_HIGH		0x00000538
#define MAC_EXTADDR_1_LOW		0x0000053c
#define MAC_EXTADDR_2_HIGH		0x00000540
#define MAC_EXTADDR_2_LOW		0x00000544
#define MAC_EXTADDR_3_HIGH		0x00000548
#define MAC_EXTADDR_3_LOW		0x0000054c
#define MAC_EXTADDR_4_HIGH		0x00000550
#define MAC_EXTADDR_4_LOW		0x00000554
#define MAC_EXTADDR_5_HIGH		0x00000558
#define MAC_EXTADDR_5_LOW		0x0000055c
#define MAC_EXTADDR_6_HIGH		0x00000560
#define MAC_EXTADDR_6_LOW		0x00000564
#define MAC_EXTADDR_7_HIGH		0x00000568
#define MAC_EXTADDR_7_LOW		0x0000056c
#define MAC_EXTADDR_8_HIGH		0x00000570
#define MAC_EXTADDR_8_LOW		0x00000574
#define MAC_EXTADDR_9_HIGH		0x00000578
#define MAC_EXTADDR_9_LOW		0x0000057c
#define MAC_EXTADDR_10_HIGH		0x00000580
#define MAC_EXTADDR_10_LOW		0x00000584
#define MAC_EXTADDR_11_HIGH		0x00000588
#define MAC_EXTADDR_11_LOW		0x0000058c
#define MAC_SERDES_CFG			0x00000590
#define  MAC_SERDES_CFG_EDGE_SELECT	 0x00001000
#define MAC_SERDES_STAT			0x00000594
/* 0x598 --> 0x5a0 unused */
#define MAC_PHYCFG1			0x000005a0
#define  MAC_PHYCFG1_RGMII_INT		 0x00000001
#define  MAC_PHYCFG1_RXCLK_TO_MASK	 0x00001ff0
#define  MAC_PHYCFG1_RXCLK_TIMEOUT	 0x00001000
#define  MAC_PHYCFG1_TXCLK_TO_MASK	 0x01ff0000
#define  MAC_PHYCFG1_TXCLK_TIMEOUT	 0x01000000
#define  MAC_PHYCFG1_RGMII_EXT_RX_DEC	 0x02000000
#define  MAC_PHYCFG1_RGMII_SND_STAT_EN	 0x04000000
#define  MAC_PHYCFG1_TXC_DRV		 0x20000000
#define MAC_PHYCFG2			0x000005a4
#define  MAC_PHYCFG2_INBAND_ENABLE	 0x00000001
#define  MAC_PHYCFG2_EMODE_MASK_MASK	 0x000001c0
#define  MAC_PHYCFG2_EMODE_MASK_AC131	 0x000000c0
#define  MAC_PHYCFG2_EMODE_MASK_50610	 0x00000100
#define  MAC_PHYCFG2_EMODE_MASK_RT8211	 0x00000000
#define  MAC_PHYCFG2_EMODE_MASK_RT8201	 0x000001c0
#define  MAC_PHYCFG2_EMODE_COMP_MASK	 0x00000e00
#define  MAC_PHYCFG2_EMODE_COMP_AC131	 0x00000600
#define  MAC_PHYCFG2_EMODE_COMP_50610	 0x00000400
#define  MAC_PHYCFG2_EMODE_COMP_RT8211	 0x00000800
#define  MAC_PHYCFG2_EMODE_COMP_RT8201	 0x00000000
#define  MAC_PHYCFG2_FMODE_MASK_MASK	 0x00007000
#define  MAC_PHYCFG2_FMODE_MASK_AC131	 0x00006000
#define  MAC_PHYCFG2_FMODE_MASK_50610	 0x00004000
#define  MAC_PHYCFG2_FMODE_MASK_RT8211	 0x00000000
#define  MAC_PHYCFG2_FMODE_MASK_RT8201	 0x00007000
#define  MAC_PHYCFG2_FMODE_COMP_MASK	 0x00038000
#define  MAC_PHYCFG2_FMODE_COMP_AC131	 0x00030000
#define  MAC_PHYCFG2_FMODE_COMP_50610	 0x00008000
#define  MAC_PHYCFG2_FMODE_COMP_RT8211	 0x00038000
#define  MAC_PHYCFG2_FMODE_COMP_RT8201	 0x00000000
#define  MAC_PHYCFG2_GMODE_MASK_MASK	 0x001c0000
#define  MAC_PHYCFG2_GMODE_MASK_AC131	 0x001c0000
#define  MAC_PHYCFG2_GMODE_MASK_50610	 0x00100000
#define  MAC_PHYCFG2_GMODE_MASK_RT8211	 0x00000000
#define  MAC_PHYCFG2_GMODE_MASK_RT8201	 0x001c0000
#define  MAC_PHYCFG2_GMODE_COMP_MASK	 0x00e00000
#define  MAC_PHYCFG2_GMODE_COMP_AC131	 0x00e00000
#define  MAC_PHYCFG2_GMODE_COMP_50610	 0x00000000
#define  MAC_PHYCFG2_GMODE_COMP_RT8211	 0x00200000
#define  MAC_PHYCFG2_GMODE_COMP_RT8201	 0x00000000
#define  MAC_PHYCFG2_ACT_MASK_MASK	 0x03000000
#define  MAC_PHYCFG2_ACT_MASK_AC131	 0x03000000
#define  MAC_PHYCFG2_ACT_MASK_50610	 0x01000000
#define  MAC_PHYCFG2_ACT_MASK_RT8211	 0x03000000
#define  MAC_PHYCFG2_ACT_MASK_RT8201	 0x01000000
#define  MAC_PHYCFG2_ACT_COMP_MASK	 0x0c000000
#define  MAC_PHYCFG2_ACT_COMP_AC131	 0x00000000
#define  MAC_PHYCFG2_ACT_COMP_50610	 0x00000000
#define  MAC_PHYCFG2_ACT_COMP_RT8211	 0x00000000
#define  MAC_PHYCFG2_ACT_COMP_RT8201	 0x08000000
#define  MAC_PHYCFG2_QUAL_MASK_MASK	 0x30000000
#define  MAC_PHYCFG2_QUAL_MASK_AC131	 0x30000000
#define  MAC_PHYCFG2_QUAL_MASK_50610	 0x30000000
#define  MAC_PHYCFG2_QUAL_MASK_RT8211	 0x30000000
#define  MAC_PHYCFG2_QUAL_MASK_RT8201	 0x30000000
#define  MAC_PHYCFG2_QUAL_COMP_MASK	 0xc0000000
#define  MAC_PHYCFG2_QUAL_COMP_AC131	 0x00000000
#define  MAC_PHYCFG2_QUAL_COMP_50610	 0x00000000
#define  MAC_PHYCFG2_QUAL_COMP_RT8211	 0x00000000
#define  MAC_PHYCFG2_QUAL_COMP_RT8201	 0x00000000
#define MAC_PHYCFG2_50610_LED_MODES \
	(MAC_PHYCFG2_EMODE_MASK_50610 | \
	 MAC_PHYCFG2_EMODE_COMP_50610 | \
	 MAC_PHYCFG2_FMODE_MASK_50610 | \
	 MAC_PHYCFG2_FMODE_COMP_50610 | \
	 MAC_PHYCFG2_GMODE_MASK_50610 | \
	 MAC_PHYCFG2_GMODE_COMP_50610 | \
	 MAC_PHYCFG2_ACT_MASK_50610 | \
	 MAC_PHYCFG2_ACT_COMP_50610 | \
	 MAC_PHYCFG2_QUAL_MASK_50610 | \
	 MAC_PHYCFG2_QUAL_COMP_50610)
#define MAC_PHYCFG2_AC131_LED_MODES \
	(MAC_PHYCFG2_EMODE_MASK_AC131 | \
	 MAC_PHYCFG2_EMODE_COMP_AC131 | \
	 MAC_PHYCFG2_FMODE_MASK_AC131 | \
	 MAC_PHYCFG2_FMODE_COMP_AC131 | \
	 MAC_PHYCFG2_GMODE_MASK_AC131 | \
	 MAC_PHYCFG2_GMODE_COMP_AC131 | \
	 MAC_PHYCFG2_ACT_MASK_AC131 | \
	 MAC_PHYCFG2_ACT_COMP_AC131 | \
	 MAC_PHYCFG2_QUAL_MASK_AC131 | \
	 MAC_PHYCFG2_QUAL_COMP_AC131)
#define MAC_PHYCFG2_RTL8211C_LED_MODES \
	(MAC_PHYCFG2_EMODE_MASK_RT8211 | \
	 MAC_PHYCFG2_EMODE_COMP_RT8211 | \
	 MAC_PHYCFG2_FMODE_MASK_RT8211 | \
	 MAC_PHYCFG2_FMODE_COMP_RT8211 | \
	 MAC_PHYCFG2_GMODE_MASK_RT8211 | \
	 MAC_PHYCFG2_GMODE_COMP_RT8211 | \
	 MAC_PHYCFG2_ACT_MASK_RT8211 | \
	 MAC_PHYCFG2_ACT_COMP_RT8211 | \
	 MAC_PHYCFG2_QUAL_MASK_RT8211 | \
	 MAC_PHYCFG2_QUAL_COMP_RT8211)
#define MAC_PHYCFG2_RTL8201E_LED_MODES \
	(MAC_PHYCFG2_EMODE_MASK_RT8201 | \
	 MAC_PHYCFG2_EMODE_COMP_RT8201 | \
	 MAC_PHYCFG2_FMODE_MASK_RT8201 | \
	 MAC_PHYCFG2_FMODE_COMP_RT8201 | \
	 MAC_PHYCFG2_GMODE_MASK_RT8201 | \
	 MAC_PHYCFG2_GMODE_COMP_RT8201 | \
	 MAC_PHYCFG2_ACT_MASK_RT8201 | \
	 MAC_PHYCFG2_ACT_COMP_RT8201 | \
	 MAC_PHYCFG2_QUAL_MASK_RT8201 | \
	 MAC_PHYCFG2_QUAL_COMP_RT8201)
#define MAC_EXT_RGMII_MODE		0x000005a8
#define  MAC_RGMII_MODE_TX_ENABLE	 0x00000001
#define  MAC_RGMII_MODE_TX_LOWPWR	 0x00000002
#define  MAC_RGMII_MODE_TX_RESET	 0x00000004
#define  MAC_RGMII_MODE_RX_INT_B	 0x00000100
#define  MAC_RGMII_MODE_RX_QUALITY	 0x00000200
#define  MAC_RGMII_MODE_RX_ACTIVITY	 0x00000400
#define  MAC_RGMII_MODE_RX_ENG_DET	 0x00000800
/* 0x5ac --> 0x5b0 unused */
#define SERDES_RX_CTRL			0x000005b0	/* 5780/5714 only */
#define  SERDES_RX_SIG_DETECT		 0x00000400
#define SG_DIG_CTRL			0x000005b0
#define  SG_DIG_USING_HW_AUTONEG	 0x80000000
#define  SG_DIG_SOFT_RESET		 0x40000000
#define  SG_DIG_DISABLE_LINKRDY		 0x20000000
#define  SG_DIG_CRC16_CLEAR_N		 0x01000000
#define  SG_DIG_EN10B			 0x00800000
#define  SG_DIG_CLEAR_STATUS		 0x00400000
#define  SG_DIG_LOCAL_DUPLEX_STATUS	 0x00200000
#define  SG_DIG_LOCAL_LINK_STATUS	 0x00100000
#define  SG_DIG_SPEED_STATUS_MASK	 0x000c0000
#define  SG_DIG_SPEED_STATUS_SHIFT	 18
#define  SG_DIG_JUMBO_PACKET_DISABLE	 0x00020000
#define  SG_DIG_RESTART_AUTONEG		 0x00010000
#define  SG_DIG_FIBER_MODE		 0x00008000
#define  SG_DIG_REMOTE_FAULT_MASK	 0x00006000
#define  SG_DIG_PAUSE_MASK		 0x00001800
#define  SG_DIG_PAUSE_CAP		 0x00000800
#define  SG_DIG_ASYM_PAUSE		 0x00001000
#define  SG_DIG_GBIC_ENABLE		 0x00000400
#define  SG_DIG_CHECK_END_ENABLE	 0x00000200
#define  SG_DIG_SGMII_AUTONEG_TIMER	 0x00000100
#define  SG_DIG_CLOCK_PHASE_SELECT	 0x00000080
#define  SG_DIG_GMII_INPUT_SELECT	 0x00000040
#define  SG_DIG_MRADV_CRC16_SELECT	 0x00000020
#define  SG_DIG_COMMA_DETECT_ENABLE	 0x00000010
#define  SG_DIG_AUTONEG_TIMER_REDUCE	 0x00000008
#define  SG_DIG_AUTONEG_LOW_ENABLE	 0x00000004
#define  SG_DIG_REMOTE_LOOPBACK		 0x00000002
#define  SG_DIG_LOOPBACK		 0x00000001
#define  SG_DIG_COMMON_SETUP (SG_DIG_CRC16_CLEAR_N | \
			      SG_DIG_LOCAL_DUPLEX_STATUS | \
			      SG_DIG_LOCAL_LINK_STATUS | \
			      (0x2 << SG_DIG_SPEED_STATUS_SHIFT) | \
			      SG_DIG_FIBER_MODE | SG_DIG_GBIC_ENABLE)
#define SG_DIG_STATUS			0x000005b4
#define  SG_DIG_CRC16_BUS_MASK		 0xffff0000
#define  SG_DIG_PARTNER_FAULT_MASK	 0x00600000 /* If !MRADV_CRC16_SELECT */
#define  SG_DIG_PARTNER_ASYM_PAUSE	 0x00100000 /* If !MRADV_CRC16_SELECT */
#define  SG_DIG_PARTNER_PAUSE_CAPABLE	 0x00080000 /* If !MRADV_CRC16_SELECT */
#define  SG_DIG_PARTNER_HALF_DUPLEX	 0x00040000 /* If !MRADV_CRC16_SELECT */
#define  SG_DIG_PARTNER_FULL_DUPLEX	 0x00020000 /* If !MRADV_CRC16_SELECT */
#define  SG_DIG_PARTNER_NEXT_PAGE	 0x00010000 /* If !MRADV_CRC16_SELECT */
#define  SG_DIG_AUTONEG_STATE_MASK	 0x00000ff0
#define  SG_DIG_IS_SERDES		 0x00000100
#define  SG_DIG_COMMA_DETECTOR		 0x00000008
#define  SG_DIG_MAC_ACK_STATUS		 0x00000004
#define  SG_DIG_AUTONEG_COMPLETE	 0x00000002
#define  SG_DIG_AUTONEG_ERROR		 0x00000001
#define TG3_TX_TSTAMP_LSB		0x000005c0
#define TG3_TX_TSTAMP_MSB		0x000005c4
#define  TG3_TSTAMP_MASK		 0x7fffffffffffffffLL
/* 0x5c8 --> 0x600 unused */
#define MAC_TX_MAC_STATE_BASE		0x00000600 /* 16 bytes */
#define MAC_RX_MAC_STATE_BASE		0x00000610 /* 20 bytes */
/* 0x624 --> 0x670 unused */

#define MAC_RSS_INDIR_TBL_0		0x00000630

#define MAC_RSS_HASH_KEY_0		0x00000670
#define MAC_RSS_HASH_KEY_1		0x00000674
#define MAC_RSS_HASH_KEY_2		0x00000678
#define MAC_RSS_HASH_KEY_3		0x0000067c
#define MAC_RSS_HASH_KEY_4		0x00000680
#define MAC_RSS_HASH_KEY_5		0x00000684
#define MAC_RSS_HASH_KEY_6		0x00000688
#define MAC_RSS_HASH_KEY_7		0x0000068c
#define MAC_RSS_HASH_KEY_8		0x00000690
#define MAC_RSS_HASH_KEY_9		0x00000694
/* 0x698 --> 0x6b0 unused */

#define TG3_RX_TSTAMP_LSB		0x000006b0
#define TG3_RX_TSTAMP_MSB		0x000006b4
/* 0x6b8 --> 0x6c8 unused */

#define TG3_RX_PTP_CTL			0x000006c8
#define TG3_RX_PTP_CTL_SYNC_EVNT	0x00000001
#define TG3_RX_PTP_CTL_DELAY_REQ	0x00000002
#define TG3_RX_PTP_CTL_PDLAY_REQ	0x00000004
#define TG3_RX_PTP_CTL_PDLAY_RES	0x00000008
#define TG3_RX_PTP_CTL_ALL_V1_EVENTS	(TG3_RX_PTP_CTL_SYNC_EVNT | \
					 TG3_RX_PTP_CTL_DELAY_REQ)
#define TG3_RX_PTP_CTL_ALL_V2_EVENTS	(TG3_RX_PTP_CTL_SYNC_EVNT | \
					 TG3_RX_PTP_CTL_DELAY_REQ | \
					 TG3_RX_PTP_CTL_PDLAY_REQ | \
					 TG3_RX_PTP_CTL_PDLAY_RES)
#define TG3_RX_PTP_CTL_FOLLOW_UP	0x00000100
#define TG3_RX_PTP_CTL_DELAY_RES	0x00000200
#define TG3_RX_PTP_CTL_PDRES_FLW_UP	0x00000400
#define TG3_RX_PTP_CTL_ANNOUNCE		0x00000800
#define TG3_RX_PTP_CTL_SIGNALING	0x00001000
#define TG3_RX_PTP_CTL_MANAGEMENT	0x00002000
#define TG3_RX_PTP_CTL_RX_PTP_V2_L2_EN	0x00800000
#define TG3_RX_PTP_CTL_RX_PTP_V2_L4_EN	0x01000000
#define TG3_RX_PTP_CTL_RX_PTP_V2_EN	(TG3_RX_PTP_CTL_RX_PTP_V2_L2_EN | \
					 TG3_RX_PTP_CTL_RX_PTP_V2_L4_EN)
#define TG3_RX_PTP_CTL_RX_PTP_V1_EN	0x02000000
#define TG3_RX_PTP_CTL_HWTS_INTERLOCK	0x04000000
/* 0x6cc --> 0x800 unused */

#define MAC_TX_STATS_OCTETS		0x00000800
#define MAC_TX_STATS_RESV1		0x00000804
#define MAC_TX_STATS_COLLISIONS		0x00000808
#define MAC_TX_STATS_XON_SENT		0x0000080c
#define MAC_TX_STATS_XOFF_SENT		0x00000810
#define MAC_TX_STATS_RESV2		0x00000814
#define MAC_TX_STATS_MAC_ERRORS		0x00000818
#define MAC_TX_STATS_SINGLE_COLLISIONS	0x0000081c
#define MAC_TX_STATS_MULT_COLLISIONS	0x00000820
#define MAC_TX_STATS_DEFERRED		0x00000824
#define MAC_TX_STATS_RESV3		0x00000828
#define MAC_TX_STATS_EXCESSIVE_COL	0x0000082c
#define MAC_TX_STATS_LATE_COL		0x00000830
#define MAC_TX_STATS_RESV4_1		0x00000834
#define MAC_TX_STATS_RESV4_2		0x00000838
#define MAC_TX_STATS_RESV4_3		0x0000083c
#define MAC_TX_STATS_RESV4_4		0x00000840
#define MAC_TX_STATS_RESV4_5		0x00000844
#define MAC_TX_STATS_RESV4_6		0x00000848
#define MAC_TX_STATS_RESV4_7		0x0000084c
#define MAC_TX_STATS_RESV4_8		0x00000850
#define MAC_TX_STATS_RESV4_9		0x00000854
#define MAC_TX_STATS_RESV4_10		0x00000858
#define MAC_TX_STATS_RESV4_11		0x0000085c
#define MAC_TX_STATS_RESV4_12		0x00000860
#define MAC_TX_STATS_RESV4_13		0x00000864
#define MAC_TX_STATS_RESV4_14		0x00000868
#define MAC_TX_STATS_UCAST		0x0000086c
#define MAC_TX_STATS_MCAST		0x00000870
#define MAC_TX_STATS_BCAST		0x00000874
#define MAC_TX_STATS_RESV5_1		0x00000878
#define MAC_TX_STATS_RESV5_2		0x0000087c
#define MAC_RX_STATS_OCTETS		0x00000880
#define MAC_RX_STATS_RESV1		0x00000884
#define MAC_RX_STATS_FRAGMENTS		0x00000888
#define MAC_RX_STATS_UCAST		0x0000088c
#define MAC_RX_STATS_MCAST		0x00000890
#define MAC_RX_STATS_BCAST		0x00000894
#define MAC_RX_STATS_FCS_ERRORS		0x00000898
#define MAC_RX_STATS_ALIGN_ERRORS	0x0000089c
#define MAC_RX_STATS_XON_PAUSE_RECVD	0x000008a0
#define MAC_RX_STATS_XOFF_PAUSE_RECVD	0x000008a4
#define MAC_RX_STATS_MAC_CTRL_RECVD	0x000008a8
#define MAC_RX_STATS_XOFF_ENTERED	0x000008ac
#define MAC_RX_STATS_FRAME_TOO_LONG	0x000008b0
#define MAC_RX_STATS_JABBERS		0x000008b4
#define MAC_RX_STATS_UNDERSIZE		0x000008b8
/* 0x8bc --> 0xc00 unused */

/* Send data initiator control registers */
#define SNDDATAI_MODE			0x00000c00
#define  SNDDATAI_MODE_RESET		 0x00000001
#define  SNDDATAI_MODE_ENABLE		 0x00000002
#define  SNDDATAI_MODE_STAT_OFLOW_ENAB	 0x00000004
#define SNDDATAI_STATUS			0x00000c04
#define  SNDDATAI_STATUS_STAT_OFLOW	 0x00000004
#define SNDDATAI_STATSCTRL		0x00000c08
#define  SNDDATAI_SCTRL_ENABLE		 0x00000001
#define  SNDDATAI_SCTRL_FASTUPD		 0x00000002
#define  SNDDATAI_SCTRL_CLEAR		 0x00000004
#define  SNDDATAI_SCTRL_FLUSH		 0x00000008
#define  SNDDATAI_SCTRL_FORCE_ZERO	 0x00000010
#define SNDDATAI_STATSENAB		0x00000c0c
#define SNDDATAI_STATSINCMASK		0x00000c10
#define ISO_PKT_TX			0x00000c20
/* 0xc24 --> 0xc80 unused */
#define SNDDATAI_COS_CNT_0		0x00000c80
#define SNDDATAI_COS_CNT_1		0x00000c84
#define SNDDATAI_COS_CNT_2		0x00000c88
#define SNDDATAI_COS_CNT_3		0x00000c8c
#define SNDDATAI_COS_CNT_4		0x00000c90
#define SNDDATAI_COS_CNT_5		0x00000c94
#define SNDDATAI_COS_CNT_6		0x00000c98
#define SNDDATAI_COS_CNT_7		0x00000c9c
#define SNDDATAI_COS_CNT_8		0x00000ca0
#define SNDDATAI_COS_CNT_9		0x00000ca4
#define SNDDATAI_COS_CNT_10		0x00000ca8
#define SNDDATAI_COS_CNT_11		0x00000cac
#define SNDDATAI_COS_CNT_12		0x00000cb0
#define SNDDATAI_COS_CNT_13		0x00000cb4
#define SNDDATAI_COS_CNT_14		0x00000cb8
#define SNDDATAI_COS_CNT_15		0x00000cbc
#define SNDDATAI_DMA_RDQ_FULL_CNT	0x00000cc0
#define SNDDATAI_DMA_PRIO_RDQ_FULL_CNT	0x00000cc4
#define SNDDATAI_SDCQ_FULL_CNT		0x00000cc8
#define SNDDATAI_NICRNG_SSND_PIDX_CNT	0x00000ccc
#define SNDDATAI_STATS_UPDATED_CNT	0x00000cd0
#define SNDDATAI_INTERRUPTS_CNT		0x00000cd4
#define SNDDATAI_AVOID_INTERRUPTS_CNT	0x00000cd8
#define SNDDATAI_SND_THRESH_HIT_CNT	0x00000cdc
/* 0xce0 --> 0x1000 unused */

/* Send data completion control registers */
#define SNDDATAC_MODE			0x00001000
#define  SNDDATAC_MODE_RESET		 0x00000001
#define  SNDDATAC_MODE_ENABLE		 0x00000002
#define  SNDDATAC_MODE_CDELAY		 0x00000010
/* 0x1004 --> 0x1400 unused */

/* Send BD ring selector */
#define SNDBDS_MODE			0x00001400
#define  SNDBDS_MODE_RESET		 0x00000001
#define  SNDBDS_MODE_ENABLE		 0x00000002
#define  SNDBDS_MODE_ATTN_ENABLE	 0x00000004
#define SNDBDS_STATUS			0x00001404
#define  SNDBDS_STATUS_ERROR_ATTN	 0x00000004
#define SNDBDS_HWDIAG			0x00001408
/* 0x140c --> 0x1440 */
#define SNDBDS_SEL_CON_IDX_0		0x00001440
#define SNDBDS_SEL_CON_IDX_1		0x00001444
#define SNDBDS_SEL_CON_IDX_2		0x00001448
#define SNDBDS_SEL_CON_IDX_3		0x0000144c
#define SNDBDS_SEL_CON_IDX_4		0x00001450
#define SNDBDS_SEL_CON_IDX_5		0x00001454
#define SNDBDS_SEL_CON_IDX_6		0x00001458
#define SNDBDS_SEL_CON_IDX_7		0x0000145c
#define SNDBDS_SEL_CON_IDX_8		0x00001460
#define SNDBDS_SEL_CON_IDX_9		0x00001464
#define SNDBDS_SEL_CON_IDX_10		0x00001468
#define SNDBDS_SEL_CON_IDX_11		0x0000146c
#define SNDBDS_SEL_CON_IDX_12		0x00001470
#define SNDBDS_SEL_CON_IDX_13		0x00001474
#define SNDBDS_SEL_CON_IDX_14		0x00001478
#define SNDBDS_SEL_CON_IDX_15		0x0000147c
/* 0x1480 --> 0x1800 unused */

/* Send BD initiator control registers */
#define SNDBDI_MODE			0x00001800
#define  SNDBDI_MODE_RESET		 0x00000001
#define  SNDBDI_MODE_ENABLE		 0x00000002
#define  SNDBDI_MODE_ATTN_ENABLE	 0x00000004
#define  SNDBDI_MODE_MULTI_TXQ_EN	 0x00000020
#define SNDBDI_STATUS			0x00001804
#define  SNDBDI_STATUS_ERROR_ATTN	 0x00000004
#define SNDBDI_IN_PROD_IDX_0		0x00001808
#define SNDBDI_IN_PROD_IDX_1		0x0000180c
#define SNDBDI_IN_PROD_IDX_2		0x00001810
#define SNDBDI_IN_PROD_IDX_3		0x00001814
#define SNDBDI_IN_PROD_IDX_4		0x00001818
#define SNDBDI_IN_PROD_IDX_5		0x0000181c
#define SNDBDI_IN_PROD_IDX_6		0x00001820
#define SNDBDI_IN_PROD_IDX_7		0x00001824
#define SNDBDI_IN_PROD_IDX_8		0x00001828
#define SNDBDI_IN_PROD_IDX_9		0x0000182c
#define SNDBDI_IN_PROD_IDX_10		0x00001830
#define SNDBDI_IN_PROD_IDX_11		0x00001834
#define SNDBDI_IN_PROD_IDX_12		0x00001838
#define SNDBDI_IN_PROD_IDX_13		0x0000183c
#define SNDBDI_IN_PROD_IDX_14		0x00001840
#define SNDBDI_IN_PROD_IDX_15		0x00001844
/* 0x1848 --> 0x1c00 unused */

/* Send BD completion control registers */
#define SNDBDC_MODE			0x00001c00
#define SNDBDC_MODE_RESET		 0x00000001
#define SNDBDC_MODE_ENABLE		 0x00000002
#define SNDBDC_MODE_ATTN_ENABLE		 0x00000004
/* 0x1c04 --> 0x2000 unused */

/* Receive list placement control registers */
#define RCVLPC_MODE			0x00002000
#define  RCVLPC_MODE_RESET		 0x00000001
#define  RCVLPC_MODE_ENABLE		 0x00000002
#define  RCVLPC_MODE_CLASS0_ATTN_ENAB	 0x00000004
#define  RCVLPC_MODE_MAPOOR_AATTN_ENAB	 0x00000008
#define  RCVLPC_MODE_STAT_OFLOW_ENAB	 0x00000010
#define RCVLPC_STATUS			0x00002004
#define  RCVLPC_STATUS_CLASS0		 0x00000004
#define  RCVLPC_STATUS_MAPOOR		 0x00000008
#define  RCVLPC_STATUS_STAT_OFLOW	 0x00000010
#define RCVLPC_LOCK			0x00002008
#define  RCVLPC_LOCK_REQ_MASK		 0x0000ffff
#define  RCVLPC_LOCK_REQ_SHIFT		 0
#define  RCVLPC_LOCK_GRANT_MASK		 0xffff0000
#define  RCVLPC_LOCK_GRANT_SHIFT	 16
#define RCVLPC_NON_EMPTY_BITS		0x0000200c
#define  RCVLPC_NON_EMPTY_BITS_MASK	 0x0000ffff
#define RCVLPC_CONFIG			0x00002010
#define RCVLPC_STATSCTRL		0x00002014
#define  RCVLPC_STATSCTRL_ENABLE	 0x00000001
#define  RCVLPC_STATSCTRL_FASTUPD	 0x00000002
#define RCVLPC_STATS_ENABLE		0x00002018
#define  RCVLPC_STATSENAB_ASF_FIX	 0x00000002
#define  RCVLPC_STATSENAB_DACK_FIX	 0x00040000
#define  RCVLPC_STATSENAB_LNGBRST_RFIX	 0x00400000
#define RCVLPC_STATS_INCMASK		0x0000201c
/* 0x2020 --> 0x2100 unused */
#define RCVLPC_SELLST_BASE		0x00002100 /* 16 16-byte entries */
#define  SELLST_TAIL			0x00000004
#define  SELLST_CONT			0x00000008
#define  SELLST_UNUSED			0x0000000c
#define RCVLPC_COS_CNTL_BASE		0x00002200 /* 16 4-byte entries */
#define RCVLPC_DROP_FILTER_CNT		0x00002240
#define RCVLPC_DMA_WQ_FULL_CNT		0x00002244
#define RCVLPC_DMA_HIPRIO_WQ_FULL_CNT	0x00002248
#define RCVLPC_NO_RCV_BD_CNT		0x0000224c
#define RCVLPC_IN_DISCARDS_CNT		0x00002250
#define RCVLPC_IN_ERRORS_CNT		0x00002254
#define RCVLPC_RCV_THRESH_HIT_CNT	0x00002258
/* 0x225c --> 0x2400 unused */

/* Receive Data and Receive BD Initiator Control */
#define RCVDBDI_MODE			0x00002400
#define  RCVDBDI_MODE_RESET		 0x00000001
#define  RCVDBDI_MODE_ENABLE		 0x00000002
#define  RCVDBDI_MODE_JUMBOBD_NEEDED	 0x00000004
#define  RCVDBDI_MODE_FRM_TOO_BIG	 0x00000008
#define  RCVDBDI_MODE_INV_RING_SZ	 0x00000010
#define  RCVDBDI_MODE_LRG_RING_SZ	 0x00010000
#define RCVDBDI_STATUS			0x00002404
#define  RCVDBDI_STATUS_JUMBOBD_NEEDED	 0x00000004
#define  RCVDBDI_STATUS_FRM_TOO_BIG	 0x00000008
#define  RCVDBDI_STATUS_INV_RING_SZ	 0x00000010
#define RCVDBDI_SPLIT_FRAME_MINSZ	0x00002408
/* 0x240c --> 0x2440 unused */
#define RCVDBDI_JUMBO_BD		0x00002440 /* TG3_BDINFO_... */
#define RCVDBDI_STD_BD			0x00002450 /* TG3_BDINFO_... */
#define RCVDBDI_MINI_BD			0x00002460 /* TG3_BDINFO_... */
#define RCVDBDI_JUMBO_CON_IDX		0x00002470
#define RCVDBDI_STD_CON_IDX		0x00002474
#define RCVDBDI_MINI_CON_IDX		0x00002478
/* 0x247c --> 0x2480 unused */
#define RCVDBDI_BD_PROD_IDX_0		0x00002480
#define RCVDBDI_BD_PROD_IDX_1		0x00002484
#define RCVDBDI_BD_PROD_IDX_2		0x00002488
#define RCVDBDI_BD_PROD_IDX_3		0x0000248c
#define RCVDBDI_BD_PROD_IDX_4		0x00002490
#define RCVDBDI_BD_PROD_IDX_5		0x00002494
#define RCVDBDI_BD_PROD_IDX_6		0x00002498
#define RCVDBDI_BD_PROD_IDX_7		0x0000249c
#define RCVDBDI_BD_PROD_IDX_8		0x000024a0
#define RCVDBDI_BD_PROD_IDX_9		0x000024a4
#define RCVDBDI_BD_PROD_IDX_10		0x000024a8
#define RCVDBDI_BD_PROD_IDX_11		0x000024ac
#define RCVDBDI_BD_PROD_IDX_12		0x000024b0
#define RCVDBDI_BD_PROD_IDX_13		0x000024b4
#define RCVDBDI_BD_PROD_IDX_14		0x000024b8
#define RCVDBDI_BD_PROD_IDX_15		0x000024bc
#define RCVDBDI_HWDIAG			0x000024c0
/* 0x24c4 --> 0x2800 unused */

/* Receive Data Completion Control */
#define RCVDCC_MODE			0x00002800
#define  RCVDCC_MODE_RESET		 0x00000001
#define  RCVDCC_MODE_ENABLE		 0x00000002
#define  RCVDCC_MODE_ATTN_ENABLE	 0x00000004
/* 0x2804 --> 0x2c00 unused */

/* Receive BD Initiator Control Registers */
#define RCVBDI_MODE			0x00002c00
#define  RCVBDI_MODE_RESET		 0x00000001
#define  RCVBDI_MODE_ENABLE		 0x00000002
#define  RCVBDI_MODE_RCB_ATTN_ENAB	 0x00000004
#define RCVBDI_STATUS			0x00002c04
#define  RCVBDI_STATUS_RCB_ATTN		 0x00000004
#define RCVBDI_JUMBO_PROD_IDX		0x00002c08
#define RCVBDI_STD_PROD_IDX		0x00002c0c
#define RCVBDI_MINI_PROD_IDX		0x00002c10
#define RCVBDI_MINI_THRESH		0x00002c14
#define RCVBDI_STD_THRESH		0x00002c18
#define RCVBDI_JUMBO_THRESH		0x00002c1c
/* 0x2c20 --> 0x2d00 unused */

#define STD_REPLENISH_LWM		0x00002d00
#define JMB_REPLENISH_LWM		0x00002d04
/* 0x2d08 --> 0x3000 unused */

/* Receive BD Completion Control Registers */
#define RCVCC_MODE			0x00003000
#define  RCVCC_MODE_RESET		 0x00000001
#define  RCVCC_MODE_ENABLE		 0x00000002
#define  RCVCC_MODE_ATTN_ENABLE		 0x00000004
#define RCVCC_STATUS			0x00003004
#define  RCVCC_STATUS_ERROR_ATTN	 0x00000004
#define RCVCC_JUMP_PROD_IDX		0x00003008
#define RCVCC_STD_PROD_IDX		0x0000300c
#define RCVCC_MINI_PROD_IDX		0x00003010
/* 0x3014 --> 0x3400 unused */

/* Receive list selector control registers */
#define RCVLSC_MODE			0x00003400
#define  RCVLSC_MODE_RESET		 0x00000001
#define  RCVLSC_MODE_ENABLE		 0x00000002
#define  RCVLSC_MODE_ATTN_ENABLE	 0x00000004
#define RCVLSC_STATUS			0x00003404
#define  RCVLSC_STATUS_ERROR_ATTN	 0x00000004
/* 0x3408 --> 0x3600 unused */

#define TG3_CPMU_DRV_STATUS		0x0000344c

/* CPMU registers */
#define TG3_CPMU_CTRL			0x00003600
#define  CPMU_CTRL_LINK_IDLE_MODE	 0x00000200
#define  CPMU_CTRL_LINK_AWARE_MODE	 0x00000400
#define  CPMU_CTRL_LINK_SPEED_MODE	 0x00004000
#define  CPMU_CTRL_GPHY_10MB_RXONLY	 0x00010000
#define TG3_CPMU_LSPD_10MB_CLK		0x00003604
#define  CPMU_LSPD_10MB_MACCLK_MASK	 0x001f0000
#define  CPMU_LSPD_10MB_MACCLK_6_25	 0x00130000
/* 0x3608 --> 0x360c unused */

#define TG3_CPMU_LSPD_1000MB_CLK	0x0000360c
#define  CPMU_LSPD_1000MB_MACCLK_62_5	 0x00000000
#define  CPMU_LSPD_1000MB_MACCLK_12_5	 0x00110000
#define  CPMU_LSPD_1000MB_MACCLK_MASK	 0x001f0000
#define TG3_CPMU_LNK_AWARE_PWRMD	0x00003610
#define  CPMU_LNK_AWARE_MACCLK_MASK	 0x001f0000
#define  CPMU_LNK_AWARE_MACCLK_6_25	 0x00130000
/* 0x3614 --> 0x361c unused */

#define TG3_CPMU_HST_ACC		0x0000361c
#define  CPMU_HST_ACC_MACCLK_MASK	 0x001f0000
#define  CPMU_HST_ACC_MACCLK_6_25	 0x00130000
/* 0x3620 --> 0x3630 unused */

#define TG3_CPMU_CLCK_ORIDE		0x00003624
#define  CPMU_CLCK_ORIDE_MAC_ORIDE_EN	 0x80000000

#define TG3_CPMU_CLCK_ORIDE_ENABLE	0x00003628
#define  TG3_CPMU_MAC_ORIDE_ENABLE	 (1 << 13)

#define TG3_CPMU_STATUS			0x0000362c
#define  TG3_CPMU_STATUS_FMSK_5717	 0x20000000
#define  TG3_CPMU_STATUS_FMSK_5719	 0xc0000000
#define  TG3_CPMU_STATUS_FSHFT_5719	 30
#define  TG3_CPMU_STATUS_LINK_MASK	 0x180000

#define TG3_CPMU_CLCK_STAT		0x00003630
#define  CPMU_CLCK_STAT_MAC_CLCK_MASK	 0x001f0000
#define  CPMU_CLCK_STAT_MAC_CLCK_62_5	 0x00000000
#define  CPMU_CLCK_STAT_MAC_CLCK_12_5	 0x00110000
#define  CPMU_CLCK_STAT_MAC_CLCK_6_25	 0x00130000
/* 0x3634 --> 0x365c unused */

#define TG3_CPMU_MUTEX_REQ		0x0000365c
#define  CPMU_MUTEX_REQ_DRIVER		 0x00001000
#define TG3_CPMU_MUTEX_GNT		0x00003660
#define  CPMU_MUTEX_GNT_DRIVER		 0x00001000
#define TG3_CPMU_PHY_STRAP		0x00003664
#define TG3_CPMU_PHY_STRAP_IS_SERDES	 0x00000020
#define TG3_CPMU_PADRNG_CTL		0x00003668
#define  TG3_CPMU_PADRNG_CTL_RDIV2	 0x00040000
/* 0x3664 --> 0x36b0 unused */

#define TG3_CPMU_EEE_MODE		0x000036b0
#define  TG3_CPMU_EEEMD_APE_TX_DET_EN	 0x00000004
#define  TG3_CPMU_EEEMD_ERLY_L1_XIT_DET	 0x00000008
#define  TG3_CPMU_EEEMD_SND_IDX_DET_EN	 0x00000040
#define  TG3_CPMU_EEEMD_LPI_ENABLE	 0x00000080
#define  TG3_CPMU_EEEMD_LPI_IN_TX	 0x00000100
#define  TG3_CPMU_EEEMD_LPI_IN_RX	 0x00000200
#define  TG3_CPMU_EEEMD_EEE_ENABLE	 0x00100000
#define TG3_CPMU_EEE_DBTMR1		0x000036b4
#define  TG3_CPMU_DBTMR1_PCIEXIT_2047US	 0x07ff0000
#define  TG3_CPMU_DBTMR1_LNKIDLE_2047US	 0x000007ff
#define  TG3_CPMU_DBTMR1_LNKIDLE_MAX	 0x0000ffff
#define TG3_CPMU_EEE_DBTMR2		0x000036b8
#define  TG3_CPMU_DBTMR2_APE_TX_2047US	 0x07ff0000
#define  TG3_CPMU_DBTMR2_TXIDXEQ_2047US	 0x000007ff
#define TG3_CPMU_EEE_LNKIDL_CTRL	0x000036bc
#define  TG3_CPMU_EEE_LNKIDL_PCIE_NL0	 0x01000000
#define  TG3_CPMU_EEE_LNKIDL_UART_IDL	 0x00000004
#define  TG3_CPMU_EEE_LNKIDL_APE_TX_MT	 0x00000002
/* 0x36c0 --> 0x36d0 unused */

#define TG3_CPMU_EEE_CTRL		0x000036d0
#define TG3_CPMU_EEE_CTRL_EXIT_16_5_US	 0x0000019d
#define TG3_CPMU_EEE_CTRL_EXIT_36_US	 0x00000384
#define TG3_CPMU_EEE_CTRL_EXIT_20_1_US	 0x000001f8
/* 0x36d4 --> 0x3800 unused */

/* Mbuf cluster free registers */
#define MBFREE_MODE			0x00003800
#define  MBFREE_MODE_RESET		 0x00000001
#define  MBFREE_MODE_ENABLE		 0x00000002
#define MBFREE_STATUS			0x00003804
/* 0x3808 --> 0x3c00 unused */

/* Host coalescing control registers */
#define HOSTCC_MODE			0x00003c00
#define  HOSTCC_MODE_RESET		 0x00000001
#define  HOSTCC_MODE_ENABLE		 0x00000002
#define  HOSTCC_MODE_ATTN		 0x00000004
#define  HOSTCC_MODE_NOW		 0x00000008
#define  HOSTCC_MODE_FULL_STATUS	 0x00000000
#define  HOSTCC_MODE_64BYTE		 0x00000080
#define  HOSTCC_MODE_32BYTE		 0x00000100
#define  HOSTCC_MODE_CLRTICK_RXBD	 0x00000200
#define  HOSTCC_MODE_CLRTICK_TXBD	 0x00000400
#define  HOSTCC_MODE_NOINT_ON_NOW	 0x00000800
#define  HOSTCC_MODE_NOINT_ON_FORCE	 0x00001000
#define  HOSTCC_MODE_COAL_VEC1_NOW	 0x00002000
#define HOSTCC_STATUS			0x00003c04
#define  HOSTCC_STATUS_ERROR_ATTN	 0x00000004
#define HOSTCC_RXCOL_TICKS		0x00003c08
#define  LOW_RXCOL_TICKS		 0x00000032
#define  LOW_RXCOL_TICKS_CLRTCKS	 0x00000014
#define  DEFAULT_RXCOL_TICKS		 0x00000048
#define  HIGH_RXCOL_TICKS		 0x00000096
#define  MAX_RXCOL_TICKS		 0x000003ff
#define HOSTCC_TXCOL_TICKS		0x00003c0c
#define  LOW_TXCOL_TICKS		 0x00000096
#define  LOW_TXCOL_TICKS_CLRTCKS	 0x00000048
#define  DEFAULT_TXCOL_TICKS		 0x0000012c
#define  HIGH_TXCOL_TICKS		 0x00000145
#define  MAX_TXCOL_TICKS		 0x000003ff
#define HOSTCC_RXMAX_FRAMES		0x00003c10
#define  LOW_RXMAX_FRAMES		 0x00000005
#define  DEFAULT_RXMAX_FRAMES		 0x00000008
#define  HIGH_RXMAX_FRAMES		 0x00000012
#define  MAX_RXMAX_FRAMES		 0x000000ff
#define HOSTCC_TXMAX_FRAMES		0x00003c14
#define  LOW_TXMAX_FRAMES		 0x00000035
#define  DEFAULT_TXMAX_FRAMES		 0x0000004b
#define  HIGH_TXMAX_FRAMES		 0x00000052
#define  MAX_TXMAX_FRAMES		 0x000000ff
#define HOSTCC_RXCOAL_TICK_INT		0x00003c18
#define  DEFAULT_RXCOAL_TICK_INT	 0x00000019
#define  DEFAULT_RXCOAL_TICK_INT_CLRTCKS 0x00000014
#define  MAX_RXCOAL_TICK_INT		 0x000003ff
#define HOSTCC_TXCOAL_TICK_INT		0x00003c1c
#define  DEFAULT_TXCOAL_TICK_INT	 0x00000019
#define  DEFAULT_TXCOAL_TICK_INT_CLRTCKS 0x00000014
#define  MAX_TXCOAL_TICK_INT		 0x000003ff
#define HOSTCC_RXCOAL_MAXF_INT		0x00003c20
#define  DEFAULT_RXCOAL_MAXF_INT	 0x00000005
#define  MAX_RXCOAL_MAXF_INT		 0x000000ff
#define HOSTCC_TXCOAL_MAXF_INT		0x00003c24
#define  DEFAULT_TXCOAL_MAXF_INT	 0x00000005
#define  MAX_TXCOAL_MAXF_INT		 0x000000ff
#define HOSTCC_STAT_COAL_TICKS		0x00003c28
#define  DEFAULT_STAT_COAL_TICKS	 0x000f4240
#define  MAX_STAT_COAL_TICKS		 0xd693d400
#define  MIN_STAT_COAL_TICKS		 0x00000064
/* 0x3c2c --> 0x3c30 unused */
#define HOSTCC_STATS_BLK_HOST_ADDR	0x00003c30 /* 64-bit */
#define HOSTCC_STATUS_BLK_HOST_ADDR	0x00003c38 /* 64-bit */
#define HOSTCC_STATS_BLK_NIC_ADDR	0x00003c40
#define HOSTCC_STATUS_BLK_NIC_ADDR	0x00003c44
#define HOSTCC_FLOW_ATTN		0x00003c48
#define HOSTCC_FLOW_ATTN_MBUF_LWM	 0x00000040
/* 0x3c4c --> 0x3c50 unused */
#define HOSTCC_JUMBO_CON_IDX		0x00003c50
#define HOSTCC_STD_CON_IDX		0x00003c54
#define HOSTCC_MINI_CON_IDX		0x00003c58
/* 0x3c5c --> 0x3c80 unused */
#define HOSTCC_RET_PROD_IDX_0		0x00003c80
#define HOSTCC_RET_PROD_IDX_1		0x00003c84
#define HOSTCC_RET_PROD_IDX_2		0x00003c88
#define HOSTCC_RET_PROD_IDX_3		0x00003c8c
#define HOSTCC_RET_PROD_IDX_4		0x00003c90
#define HOSTCC_RET_PROD_IDX_5		0x00003c94
#define HOSTCC_RET_PROD_IDX_6		0x00003c98
#define HOSTCC_RET_PROD_IDX_7		0x00003c9c
#define HOSTCC_RET_PROD_IDX_8		0x00003ca0
#define HOSTCC_RET_PROD_IDX_9		0x00003ca4
#define HOSTCC_RET_PROD_IDX_10		0x00003ca8
#define HOSTCC_RET_PROD_IDX_11		0x00003cac
#define HOSTCC_RET_PROD_IDX_12		0x00003cb0
#define HOSTCC_RET_PROD_IDX_13		0x00003cb4
#define HOSTCC_RET_PROD_IDX_14		0x00003cb8
#define HOSTCC_RET_PROD_IDX_15		0x00003cbc
#define HOSTCC_SND_CON_IDX_0		0x00003cc0
#define HOSTCC_SND_CON_IDX_1		0x00003cc4
#define HOSTCC_SND_CON_IDX_2		0x00003cc8
#define HOSTCC_SND_CON_IDX_3		0x00003ccc
#define HOSTCC_SND_CON_IDX_4		0x00003cd0
#define HOSTCC_SND_CON_IDX_5		0x00003cd4
#define HOSTCC_SND_CON_IDX_6		0x00003cd8
#define HOSTCC_SND_CON_IDX_7		0x00003cdc
#define HOSTCC_SND_CON_IDX_8		0x00003ce0
#define HOSTCC_SND_CON_IDX_9		0x00003ce4
#define HOSTCC_SND_CON_IDX_10		0x00003ce8
#define HOSTCC_SND_CON_IDX_11		0x00003cec
#define HOSTCC_SND_CON_IDX_12		0x00003cf0
#define HOSTCC_SND_CON_IDX_13		0x00003cf4
#define HOSTCC_SND_CON_IDX_14		0x00003cf8
#define HOSTCC_SND_CON_IDX_15		0x00003cfc
#define HOSTCC_STATBLCK_RING1		0x00003d00
/* 0x3d00 --> 0x3d80 unused */

#define HOSTCC_RXCOL_TICKS_VEC1		0x00003d80
#define HOSTCC_TXCOL_TICKS_VEC1		0x00003d84
#define HOSTCC_RXMAX_FRAMES_VEC1	0x00003d88
#define HOSTCC_TXMAX_FRAMES_VEC1	0x00003d8c
#define HOSTCC_RXCOAL_MAXF_INT_VEC1	0x00003d90
#define HOSTCC_TXCOAL_MAXF_INT_VEC1	0x00003d94
/* 0x3d98 --> 0x4000 unused */

/* Memory arbiter control registers */
#define MEMARB_MODE			0x00004000
#define  MEMARB_MODE_RESET		 0x00000001
#define  MEMARB_MODE_ENABLE		 0x00000002
#define MEMARB_STATUS			0x00004004
#define MEMARB_TRAP_ADDR_LOW		0x00004008
#define MEMARB_TRAP_ADDR_HIGH		0x0000400c
/* 0x4010 --> 0x4400 unused */

/* Buffer manager control registers */
#define BUFMGR_MODE			0x00004400
#define  BUFMGR_MODE_RESET		 0x00000001
#define  BUFMGR_MODE_ENABLE		 0x00000002
#define  BUFMGR_MODE_ATTN_ENABLE	 0x00000004
#define  BUFMGR_MODE_BM_TEST		 0x00000008
#define  BUFMGR_MODE_MBLOW_ATTN_ENAB	 0x00000010
#define  BUFMGR_MODE_NO_TX_UNDERRUN	 0x80000000
#define BUFMGR_STATUS			0x00004404
#define  BUFMGR_STATUS_ERROR		 0x00000004
#define  BUFMGR_STATUS_MBLOW		 0x00000010
#define BUFMGR_MB_POOL_ADDR		0x00004408
#define BUFMGR_MB_POOL_SIZE		0x0000440c
#define BUFMGR_MB_RDMA_LOW_WATER	0x00004410
#define  DEFAULT_MB_RDMA_LOW_WATER	 0x00000050
#define  DEFAULT_MB_RDMA_LOW_WATER_5705	 0x00000000
#define  DEFAULT_MB_RDMA_LOW_WATER_JUMBO 0x00000130
#define  DEFAULT_MB_RDMA_LOW_WATER_JUMBO_5780 0x00000000
#define BUFMGR_MB_MACRX_LOW_WATER	0x00004414
#define  DEFAULT_MB_MACRX_LOW_WATER	  0x00000020
#define  DEFAULT_MB_MACRX_LOW_WATER_5705  0x00000010
#define  DEFAULT_MB_MACRX_LOW_WATER_5906  0x00000004
#define  DEFAULT_MB_MACRX_LOW_WATER_57765 0x0000002a
#define  DEFAULT_MB_MACRX_LOW_WATER_JUMBO 0x00000098
#define  DEFAULT_MB_MACRX_LOW_WATER_JUMBO_5780 0x0000004b
#define  DEFAULT_MB_MACRX_LOW_WATER_JUMBO_57765 0x0000007e
#define BUFMGR_MB_HIGH_WATER		0x00004418
#define  DEFAULT_MB_HIGH_WATER		 0x00000060
#define  DEFAULT_MB_HIGH_WATER_5705	 0x00000060
#define  DEFAULT_MB_HIGH_WATER_5906	 0x00000010
#define  DEFAULT_MB_HIGH_WATER_57765	 0x000000a0
#define  DEFAULT_MB_HIGH_WATER_JUMBO	 0x0000017c
#define  DEFAULT_MB_HIGH_WATER_JUMBO_5780 0x00000096
#define  DEFAULT_MB_HIGH_WATER_JUMBO_57765 0x000000ea
#define BUFMGR_RX_MB_ALLOC_REQ		0x0000441c
#define  BUFMGR_MB_ALLOC_BIT		 0x10000000
#define BUFMGR_RX_MB_ALLOC_RESP		0x00004420
#define BUFMGR_TX_MB_ALLOC_REQ		0x00004424
#define BUFMGR_TX_MB_ALLOC_RESP		0x00004428
#define BUFMGR_DMA_DESC_POOL_ADDR	0x0000442c
#define BUFMGR_DMA_DESC_POOL_SIZE	0x00004430
#define BUFMGR_DMA_LOW_WATER		0x00004434
#define  DEFAULT_DMA_LOW_WATER		 0x00000005
#define BUFMGR_DMA_HIGH_WATER		0x00004438
#define  DEFAULT_DMA_HIGH_WATER		 0x0000000a
#define BUFMGR_RX_DMA_ALLOC_REQ		0x0000443c
#define BUFMGR_RX_DMA_ALLOC_RESP	0x00004440
#define BUFMGR_TX_DMA_ALLOC_REQ		0x00004444
#define BUFMGR_TX_DMA_ALLOC_RESP	0x00004448
#define BUFMGR_HWDIAG_0			0x0000444c
#define BUFMGR_HWDIAG_1			0x00004450
#define BUFMGR_HWDIAG_2			0x00004454
/* 0x4458 --> 0x4800 unused */

/* Read DMA control registers */
#define RDMAC_MODE			0x00004800
#define  RDMAC_MODE_RESET		 0x00000001
#define  RDMAC_MODE_ENABLE		 0x00000002
#define  RDMAC_MODE_TGTABORT_ENAB	 0x00000004
#define  RDMAC_MODE_MSTABORT_ENAB	 0x00000008
#define  RDMAC_MODE_PARITYERR_ENAB	 0x00000010
#define  RDMAC_MODE_ADDROFLOW_ENAB	 0x00000020
#define  RDMAC_MODE_FIFOOFLOW_ENAB	 0x00000040
#define  RDMAC_MODE_FIFOURUN_ENAB	 0x00000080
#define  RDMAC_MODE_FIFOOREAD_ENAB	 0x00000100
#define  RDMAC_MODE_LNGREAD_ENAB	 0x00000200
#define  RDMAC_MODE_SPLIT_ENABLE	 0x00000800
#define  RDMAC_MODE_BD_SBD_CRPT_ENAB	 0x00000800
#define  RDMAC_MODE_SPLIT_RESET		 0x00001000
#define  RDMAC_MODE_MBUF_RBD_CRPT_ENAB	 0x00001000
#define  RDMAC_MODE_MBUF_SBD_CRPT_ENAB	 0x00002000
#define  RDMAC_MODE_FIFO_SIZE_128	 0x00020000
#define  RDMAC_MODE_FIFO_LONG_BURST	 0x00030000
#define  RDMAC_MODE_JMB_2K_MMRR		 0x00800000
#define  RDMAC_MODE_MULT_DMA_RD_DIS	 0x01000000
#define  RDMAC_MODE_IPV4_LSO_EN		 0x08000000
#define  RDMAC_MODE_IPV6_LSO_EN		 0x10000000
#define  RDMAC_MODE_H2BNC_VLAN_DET	 0x20000000
#define RDMAC_STATUS			0x00004804
#define  RDMAC_STATUS_TGTABORT		 0x00000004
#define  RDMAC_STATUS_MSTABORT		 0x00000008
#define  RDMAC_STATUS_PARITYERR		 0x00000010
#define  RDMAC_STATUS_ADDROFLOW		 0x00000020
#define  RDMAC_STATUS_FIFOOFLOW		 0x00000040
#define  RDMAC_STATUS_FIFOURUN		 0x00000080
#define  RDMAC_STATUS_FIFOOREAD		 0x00000100
#define  RDMAC_STATUS_LNGREAD		 0x00000200
/* 0x4808 --> 0x4890 unused */

#define TG3_RDMA_RSRVCTRL_REG2		0x00004890
#define TG3_LSO_RD_DMA_CRPTEN_CTRL2	0x000048a0

#define TG3_RDMA_RSRVCTRL_REG		0x00004900
#define TG3_RDMA_RSRVCTRL_FIFO_OFLW_FIX	 0x00000004
#define TG3_RDMA_RSRVCTRL_FIFO_LWM_1_5K	 0x00000c00
#define TG3_RDMA_RSRVCTRL_FIFO_LWM_MASK	 0x00000ff0
#define TG3_RDMA_RSRVCTRL_FIFO_HWM_1_5K	 0x000c0000
#define TG3_RDMA_RSRVCTRL_FIFO_HWM_MASK	 0x000ff000
#define TG3_RDMA_RSRVCTRL_TXMRGN_320B	 0x28000000
#define TG3_RDMA_RSRVCTRL_TXMRGN_MASK	 0xffe00000
/* 0x4904 --> 0x4910 unused */

#define TG3_LSO_RD_DMA_CRPTEN_CTRL	0x00004910
#define TG3_LSO_RD_DMA_CRPTEN_CTRL_BLEN_BD_4K	 0x00030000
#define TG3_LSO_RD_DMA_CRPTEN_CTRL_BLEN_LSO_4K	 0x000c0000
#define TG3_LSO_RD_DMA_TX_LENGTH_WA_5719	 0x02000000
#define TG3_LSO_RD_DMA_TX_LENGTH_WA_5720	 0x00200000
/* 0x4914 --> 0x4be0 unused */

#define TG3_NUM_RDMA_CHANNELS		4
#define TG3_RDMA_LENGTH			0x00004be0

/* Write DMA control registers */
#define WDMAC_MODE			0x00004c00
#define  WDMAC_MODE_RESET		 0x00000001
#define  WDMAC_MODE_ENABLE		 0x00000002
#define  WDMAC_MODE_TGTABORT_ENAB	 0x00000004
#define  WDMAC_MODE_MSTABORT_ENAB	 0x00000008
#define  WDMAC_MODE_PARITYERR_ENAB	 0x00000010
#define  WDMAC_MODE_ADDROFLOW_ENAB	 0x00000020
#define  WDMAC_MODE_FIFOOFLOW_ENAB	 0x00000040
#define  WDMAC_MODE_FIFOURUN_ENAB	 0x00000080
#define  WDMAC_MODE_FIFOOREAD_ENAB	 0x00000100
#define  WDMAC_MODE_LNGREAD_ENAB	 0x00000200
#define  WDMAC_MODE_RX_ACCEL		 0x00000400
#define  WDMAC_MODE_STATUS_TAG_FIX	 0x20000000
#define  WDMAC_MODE_BURST_ALL_DATA	 0xc0000000
#define WDMAC_STATUS			0x00004c04
#define  WDMAC_STATUS_TGTABORT		 0x00000004
#define  WDMAC_STATUS_MSTABORT		 0x00000008
#define  WDMAC_STATUS_PARITYERR		 0x00000010
#define  WDMAC_STATUS_ADDROFLOW		 0x00000020
#define  WDMAC_STATUS_FIFOOFLOW		 0x00000040
#define  WDMAC_STATUS_FIFOURUN		 0x00000080
#define  WDMAC_STATUS_FIFOOREAD		 0x00000100
#define  WDMAC_STATUS_LNGREAD		 0x00000200
/* 0x4c08 --> 0x5000 unused */

/* Per-cpu register offsets (arm9) */
#define CPU_MODE			0x00000000
#define  CPU_MODE_RESET			 0x00000001
#define  CPU_MODE_HALT			 0x00000400
#define CPU_STATE			0x00000004
#define CPU_EVTMASK			0x00000008
/* 0xc --> 0x1c reserved */
#define CPU_PC				0x0000001c
#define CPU_INSN			0x00000020
#define CPU_SPAD_UFLOW			0x00000024
#define CPU_WDOG_CLEAR			0x00000028
#define CPU_WDOG_VECTOR			0x0000002c
#define CPU_WDOG_PC			0x00000030
#define CPU_HW_BP			0x00000034
/* 0x38 --> 0x44 unused */
#define CPU_WDOG_SAVED_STATE		0x00000044
#define CPU_LAST_BRANCH_ADDR		0x00000048
#define CPU_SPAD_UFLOW_SET		0x0000004c
/* 0x50 --> 0x200 unused */
#define CPU_R0				0x00000200
#define CPU_R1				0x00000204
#define CPU_R2				0x00000208
#define CPU_R3				0x0000020c
#define CPU_R4				0x00000210
#define CPU_R5				0x00000214
#define CPU_R6				0x00000218
#define CPU_R7				0x0000021c
#define CPU_R8				0x00000220
#define CPU_R9				0x00000224
#define CPU_R10				0x00000228
#define CPU_R11				0x0000022c
#define CPU_R12				0x00000230
#define CPU_R13				0x00000234
#define CPU_R14				0x00000238
#define CPU_R15				0x0000023c
#define CPU_R16				0x00000240
#define CPU_R17				0x00000244
#define CPU_R18				0x00000248
#define CPU_R19				0x0000024c
#define CPU_R20				0x00000250
#define CPU_R21				0x00000254
#define CPU_R22				0x00000258
#define CPU_R23				0x0000025c
#define CPU_R24				0x00000260
#define CPU_R25				0x00000264
#define CPU_R26				0x00000268
#define CPU_R27				0x0000026c
#define CPU_R28				0x00000270
#define CPU_R29				0x00000274
#define CPU_R30				0x00000278
#define CPU_R31				0x0000027c
/* 0x280 --> 0x400 unused */

#define RX_CPU_BASE			0x00005000
#define RX_CPU_MODE			0x00005000
#define RX_CPU_STATE			0x00005004
#define RX_CPU_PGMCTR			0x0000501c
#define RX_CPU_HWBKPT			0x00005034
#define TX_CPU_BASE			0x00005400
#define TX_CPU_MODE			0x00005400
#define TX_CPU_STATE			0x00005404
#define TX_CPU_PGMCTR			0x0000541c

#define VCPU_STATUS			0x00005100
#define  VCPU_STATUS_INIT_DONE		 0x04000000
#define  VCPU_STATUS_DRV_RESET		 0x08000000

#define VCPU_CFGSHDW			0x00005104
#define  VCPU_CFGSHDW_WOL_ENABLE	 0x00000001
#define  VCPU_CFGSHDW_WOL_MAGPKT	 0x00000004
#define  VCPU_CFGSHDW_ASPM_DBNC		 0x00001000

/* Mailboxes */
#define GRCMBOX_BASE			0x00005600
#define GRCMBOX_INTERRUPT_0		0x00005800 /* 64-bit */
#define GRCMBOX_INTERRUPT_1		0x00005808 /* 64-bit */
#define GRCMBOX_INTERRUPT_2		0x00005810 /* 64-bit */
#define GRCMBOX_INTERRUPT_3		0x00005818 /* 64-bit */
#define GRCMBOX_GENERAL_0		0x00005820 /* 64-bit */
#define GRCMBOX_GENERAL_1		0x00005828 /* 64-bit */
#define GRCMBOX_GENERAL_2		0x00005830 /* 64-bit */
#define GRCMBOX_GENERAL_3		0x00005838 /* 64-bit */
#define GRCMBOX_GENERAL_4		0x00005840 /* 64-bit */
#define GRCMBOX_GENERAL_5		0x00005848 /* 64-bit */
#define GRCMBOX_GENERAL_6		0x00005850 /* 64-bit */
#define GRCMBOX_GENERAL_7		0x00005858 /* 64-bit */
#define GRCMBOX_RELOAD_STAT		0x00005860 /* 64-bit */
#define GRCMBOX_RCVSTD_PROD_IDX		0x00005868 /* 64-bit */
#define GRCMBOX_RCVJUMBO_PROD_IDX	0x00005870 /* 64-bit */
#define GRCMBOX_RCVMINI_PROD_IDX	0x00005878 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_0	0x00005880 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_1	0x00005888 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_2	0x00005890 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_3	0x00005898 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_4	0x000058a0 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_5	0x000058a8 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_6	0x000058b0 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_7	0x000058b8 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_8	0x000058c0 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_9	0x000058c8 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_10	0x000058d0 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_11	0x000058d8 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_12	0x000058e0 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_13	0x000058e8 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_14	0x000058f0 /* 64-bit */
#define GRCMBOX_RCVRET_CON_IDX_15	0x000058f8 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_0	0x00005900 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_1	0x00005908 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_2	0x00005910 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_3	0x00005918 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_4	0x00005920 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_5	0x00005928 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_6	0x00005930 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_7	0x00005938 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_8	0x00005940 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_9	0x00005948 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_10	0x00005950 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_11	0x00005958 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_12	0x00005960 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_13	0x00005968 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_14	0x00005970 /* 64-bit */
#define GRCMBOX_SNDHOST_PROD_IDX_15	0x00005978 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_0	0x00005980 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_1	0x00005988 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_2	0x00005990 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_3	0x00005998 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_4	0x000059a0 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_5	0x000059a8 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_6	0x000059b0 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_7	0x000059b8 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_8	0x000059c0 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_9	0x000059c8 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_10	0x000059d0 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_11	0x000059d8 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_12	0x000059e0 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_13	0x000059e8 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_14	0x000059f0 /* 64-bit */
#define GRCMBOX_SNDNIC_PROD_IDX_15	0x000059f8 /* 64-bit */
#define GRCMBOX_HIGH_PRIO_EV_VECTOR	0x00005a00
#define GRCMBOX_HIGH_PRIO_EV_MASK	0x00005a04
#define GRCMBOX_LOW_PRIO_EV_VEC		0x00005a08
#define GRCMBOX_LOW_PRIO_EV_MASK	0x00005a0c
/* 0x5a10 --> 0x5c00 */

/* Flow Through queues */
#define FTQ_RESET			0x00005c00
/* 0x5c04 --> 0x5c10 unused */
#define FTQ_DMA_NORM_READ_CTL		0x00005c10
#define FTQ_DMA_NORM_READ_FULL_CNT	0x00005c14
#define FTQ_DMA_NORM_READ_FIFO_ENQDEQ	0x00005c18
#define FTQ_DMA_NORM_READ_WRITE_PEEK	0x00005c1c
#define FTQ_DMA_HIGH_READ_CTL		0x00005c20
#define FTQ_DMA_HIGH_READ_FULL_CNT	0x00005c24
#define FTQ_DMA_HIGH_READ_FIFO_ENQDEQ	0x00005c28
#define FTQ_DMA_HIGH_READ_WRITE_PEEK	0x00005c2c
#define FTQ_DMA_COMP_DISC_CTL		0x00005c30
#define FTQ_DMA_COMP_DISC_FULL_CNT	0x00005c34
#define FTQ_DMA_COMP_DISC_FIFO_ENQDEQ	0x00005c38
#define FTQ_DMA_COMP_DISC_WRITE_PEEK	0x00005c3c
#define FTQ_SEND_BD_COMP_CTL		0x00005c40
#define FTQ_SEND_BD_COMP_FULL_CNT	0x00005c44
#define FTQ_SEND_BD_COMP_FIFO_ENQDEQ	0x00005c48
#define FTQ_SEND_BD_COMP_WRITE_PEEK	0x00005c4c
#define FTQ_SEND_DATA_INIT_CTL		0x00005c50
#define FTQ_SEND_DATA_INIT_FULL_CNT	0x00005c54
#define FTQ_SEND_DATA_INIT_FIFO_ENQDEQ	0x00005c58
#define FTQ_SEND_DATA_INIT_WRITE_PEEK	0x00005c5c
#define FTQ_DMA_NORM_WRITE_CTL		0x00005c60
#define FTQ_DMA_NORM_WRITE_FULL_CNT	0x00005c64
#define FTQ_DMA_NORM_WRITE_FIFO_ENQDEQ	0x00005c68
#define FTQ_DMA_NORM_WRITE_WRITE_PEEK	0x00005c6c
#define FTQ_DMA_HIGH_WRITE_CTL		0x00005c70
#define FTQ_DMA_HIGH_WRITE_FULL_CNT	0x00005c74
#define FTQ_DMA_HIGH_WRITE_FIFO_ENQDEQ	0x00005c78
#define FTQ_DMA_HIGH_WRITE_WRITE_PEEK	0x00005c7c
#define FTQ_SWTYPE1_CTL			0x00005c80
#define FTQ_SWTYPE1_FULL_CNT		0x00005c84
#define FTQ_SWTYPE1_FIFO_ENQDEQ		0x00005c88
#define FTQ_SWTYPE1_WRITE_PEEK		0x00005c8c
#define FTQ_SEND_DATA_COMP_CTL		0x00005c90
#define FTQ_SEND_DATA_COMP_FULL_CNT	0x00005c94
#define FTQ_SEND_DATA_COMP_FIFO_ENQDEQ	0x00005c98
#define FTQ_SEND_DATA_COMP_WRITE_PEEK	0x00005c9c
#define FTQ_HOST_COAL_CTL		0x00005ca0
#define FTQ_HOST_COAL_FULL_CNT		0x00005ca4
#define FTQ_HOST_COAL_FIFO_ENQDEQ	0x00005ca8
#define FTQ_HOST_COAL_WRITE_PEEK	0x00005cac
#define FTQ_MAC_TX_CTL			0x00005cb0
#define FTQ_MAC_TX_FULL_CNT		0x00005cb4
#define FTQ_MAC_TX_FIFO_ENQDEQ		0x00005cb8
#define FTQ_MAC_TX_WRITE_PEEK		0x00005cbc
#define FTQ_MB_FREE_CTL			0x00005cc0
#define FTQ_MB_FREE_FULL_CNT		0x00005cc4
#define FTQ_MB_FREE_FIFO_ENQDEQ		0x00005cc8
#define FTQ_MB_FREE_WRITE_PEEK		0x00005ccc
#define FTQ_RCVBD_COMP_CTL		0x00005cd0
#define FTQ_RCVBD_COMP_FULL_CNT		0x00005cd4
#define FTQ_RCVBD_COMP_FIFO_ENQDEQ	0x00005cd8
#define FTQ_RCVBD_COMP_WRITE_PEEK	0x00005cdc
#define FTQ_RCVLST_PLMT_CTL		0x00005ce0
#define FTQ_RCVLST_PLMT_FULL_CNT	0x00005ce4
#define FTQ_RCVLST_PLMT_FIFO_ENQDEQ	0x00005ce8
#define FTQ_RCVLST_PLMT_WRITE_PEEK	0x00005cec
#define FTQ_RCVDATA_INI_CTL		0x00005cf0
#define FTQ_RCVDATA_INI_FULL_CNT	0x00005cf4
#define FTQ_RCVDATA_INI_FIFO_ENQDEQ	0x00005cf8
#define FTQ_RCVDATA_INI_WRITE_PEEK	0x00005cfc
#define FTQ_RCVDATA_COMP_CTL		0x00005d00
#define FTQ_RCVDATA_COMP_FULL_CNT	0x00005d04
#define FTQ_RCVDATA_COMP_FIFO_ENQDEQ	0x00005d08
#define FTQ_RCVDATA_COMP_WRITE_PEEK	0x00005d0c
#define FTQ_SWTYPE2_CTL			0x00005d10
#define FTQ_SWTYPE2_FULL_CNT		0x00005d14
#define FTQ_SWTYPE2_FIFO_ENQDEQ		0x00005d18
#define FTQ_SWTYPE2_WRITE_PEEK		0x00005d1c
/* 0x5d20 --> 0x6000 unused */

/* Message signaled interrupt registers */
#define MSGINT_MODE			0x00006000
#define  MSGINT_MODE_RESET		 0x00000001
#define  MSGINT_MODE_ENABLE		 0x00000002
#define  MSGINT_MODE_ONE_SHOT_DISABLE	 0x00000020
#define  MSGINT_MODE_MULTIVEC_EN	 0x00000080
#define MSGINT_STATUS			0x00006004
#define  MSGINT_STATUS_MSI_REQ		 0x00000001
#define MSGINT_FIFO			0x00006008
/* 0x600c --> 0x6400 unused */

/* DMA completion registers */
#define DMAC_MODE			0x00006400
#define  DMAC_MODE_RESET		 0x00000001
#define  DMAC_MODE_ENABLE		 0x00000002
/* 0x6404 --> 0x6800 unused */

/* GRC registers */
#define GRC_MODE			0x00006800
#define  GRC_MODE_UPD_ON_COAL		0x00000001
#define  GRC_MODE_BSWAP_NONFRM_DATA	0x00000002
#define  GRC_MODE_WSWAP_NONFRM_DATA	0x00000004
#define  GRC_MODE_BSWAP_DATA		0x00000010
#define  GRC_MODE_WSWAP_DATA		0x00000020
#define  GRC_MODE_BYTE_SWAP_B2HRX_DATA	0x00000040
#define  GRC_MODE_WORD_SWAP_B2HRX_DATA	0x00000080
#define  GRC_MODE_SPLITHDR		0x00000100
#define  GRC_MODE_NOFRM_CRACKING	0x00000200
#define  GRC_MODE_INCL_CRC		0x00000400
#define  GRC_MODE_ALLOW_BAD_FRMS	0x00000800
#define  GRC_MODE_NOIRQ_ON_SENDS	0x00002000
#define  GRC_MODE_NOIRQ_ON_RCV		0x00004000
#define  GRC_MODE_FORCE_PCI32BIT	0x00008000
#define  GRC_MODE_B2HRX_ENABLE		0x00008000
#define  GRC_MODE_HOST_STACKUP		0x00010000
#define  GRC_MODE_HOST_SENDBDS		0x00020000
#define  GRC_MODE_HTX2B_ENABLE		0x00040000
#define  GRC_MODE_TIME_SYNC_ENABLE	0x00080000
#define  GRC_MODE_NO_TX_PHDR_CSUM	0x00100000
#define  GRC_MODE_NVRAM_WR_ENABLE	0x00200000
#define  GRC_MODE_PCIE_TL_SEL		0x00000000
#define  GRC_MODE_PCIE_PL_SEL		0x00400000
#define  GRC_MODE_NO_RX_PHDR_CSUM	0x00800000
#define  GRC_MODE_IRQ_ON_TX_CPU_ATTN	0x01000000
#define  GRC_MODE_IRQ_ON_RX_CPU_ATTN	0x02000000
#define  GRC_MODE_IRQ_ON_MAC_ATTN	0x04000000
#define  GRC_MODE_IRQ_ON_DMA_ATTN	0x08000000
#define  GRC_MODE_IRQ_ON_FLOW_ATTN	0x10000000
#define  GRC_MODE_4X_NIC_SEND_RINGS	0x20000000
#define  GRC_MODE_PCIE_DL_SEL		0x20000000
#define  GRC_MODE_MCAST_FRM_ENABLE	0x40000000
#define  GRC_MODE_PCIE_HI_1K_EN		0x80000000
#define  GRC_MODE_PCIE_PORT_MASK	(GRC_MODE_PCIE_TL_SEL | \
					 GRC_MODE_PCIE_PL_SEL | \
					 GRC_MODE_PCIE_DL_SEL | \
					 GRC_MODE_PCIE_HI_1K_EN)
#define GRC_MISC_CFG			0x00006804
#define  GRC_MISC_CFG_CORECLK_RESET	0x00000001
#define  GRC_MISC_CFG_PRESCALAR_MASK	0x000000fe
#define  GRC_MISC_CFG_PRESCALAR_SHIFT	1
#define  GRC_MISC_CFG_BOARD_ID_MASK	0x0001e000
#define  GRC_MISC_CFG_BOARD_ID_5700	0x0001e000
#define  GRC_MISC_CFG_BOARD_ID_5701	0x00000000
#define  GRC_MISC_CFG_BOARD_ID_5702FE	0x00004000
#define  GRC_MISC_CFG_BOARD_ID_5703	0x00000000
#define  GRC_MISC_CFG_BOARD_ID_5703S	0x00002000
#define  GRC_MISC_CFG_BOARD_ID_5704	0x00000000
#define  GRC_MISC_CFG_BOARD_ID_5704CIOBE 0x00004000
#define  GRC_MISC_CFG_BOARD_ID_5704_A2	0x00008000
#define  GRC_MISC_CFG_BOARD_ID_5788	0x00010000
#define  GRC_MISC_CFG_BOARD_ID_5788M	0x00018000
#define  GRC_MISC_CFG_BOARD_ID_AC91002A1 0x00018000
#define  GRC_MISC_CFG_EPHY_IDDQ		0x00200000
#define  GRC_MISC_CFG_KEEP_GPHY_POWER	0x04000000
#define GRC_LOCAL_CTRL			0x00006808
#define  GRC_LCLCTRL_INT_ACTIVE		0x00000001
#define  GRC_LCLCTRL_CLEARINT		0x00000002
#define  GRC_LCLCTRL_SETINT		0x00000004
#define  GRC_LCLCTRL_INT_ON_ATTN	0x00000008
#define  GRC_LCLCTRL_GPIO_UART_SEL	0x00000010	/* 5755 only */
#define  GRC_LCLCTRL_USE_SIG_DETECT	0x00000010	/* 5714/5780 only */
#define  GRC_LCLCTRL_USE_EXT_SIG_DETECT	0x00000020	/* 5714/5780 only */
#define  GRC_LCLCTRL_GPIO_INPUT3	0x00000020
#define  GRC_LCLCTRL_GPIO_OE3		0x00000040
#define  GRC_LCLCTRL_GPIO_OUTPUT3	0x00000080
#define  GRC_LCLCTRL_GPIO_INPUT0	0x00000100
#define  GRC_LCLCTRL_GPIO_INPUT1	0x00000200
#define  GRC_LCLCTRL_GPIO_INPUT2	0x00000400
#define  GRC_LCLCTRL_GPIO_OE0		0x00000800
#define  GRC_LCLCTRL_GPIO_OE1		0x00001000
#define  GRC_LCLCTRL_GPIO_OE2		0x00002000
#define  GRC_LCLCTRL_GPIO_OUTPUT0	0x00004000
#define  GRC_LCLCTRL_GPIO_OUTPUT1	0x00008000
#define  GRC_LCLCTRL_GPIO_OUTPUT2	0x00010000
#define  GRC_LCLCTRL_EXTMEM_ENABLE	0x00020000
#define  GRC_LCLCTRL_MEMSZ_MASK		0x001c0000
#define  GRC_LCLCTRL_MEMSZ_256K		0x00000000
#define  GRC_LCLCTRL_MEMSZ_512K		0x00040000
#define  GRC_LCLCTRL_MEMSZ_1M		0x00080000
#define  GRC_LCLCTRL_MEMSZ_2M		0x000c0000
#define  GRC_LCLCTRL_MEMSZ_4M		0x00100000
#define  GRC_LCLCTRL_MEMSZ_8M		0x00140000
#define  GRC_LCLCTRL_MEMSZ_16M		0x00180000
#define  GRC_LCLCTRL_BANK_SELECT	0x00200000
#define  GRC_LCLCTRL_SSRAM_TYPE		0x00400000
#define  GRC_LCLCTRL_AUTO_SEEPROM	0x01000000
#define GRC_TIMER			0x0000680c
#define GRC_RX_CPU_EVENT		0x00006810
#define  GRC_RX_CPU_DRIVER_EVENT	0x00004000
#define GRC_RX_TIMER_REF		0x00006814
#define GRC_RX_CPU_SEM			0x00006818
#define GRC_REMOTE_RX_CPU_ATTN		0x0000681c
#define GRC_TX_CPU_EVENT		0x00006820
#define GRC_TX_TIMER_REF		0x00006824
#define GRC_TX_CPU_SEM			0x00006828
#define GRC_REMOTE_TX_CPU_ATTN		0x0000682c
#define GRC_MEM_POWER_UP		0x00006830 /* 64-bit */
#define GRC_EEPROM_ADDR			0x00006838
#define  EEPROM_ADDR_WRITE		0x00000000
#define  EEPROM_ADDR_READ		0x80000000
#define  EEPROM_ADDR_COMPLETE		0x40000000
#define  EEPROM_ADDR_FSM_RESET		0x20000000
#define  EEPROM_ADDR_DEVID_MASK		0x1c000000
#define  EEPROM_ADDR_DEVID_SHIFT	26
#define  EEPROM_ADDR_START		0x02000000
#define  EEPROM_ADDR_CLKPERD_SHIFT	16
#define  EEPROM_ADDR_ADDR_MASK		0x0000ffff
#define  EEPROM_ADDR_ADDR_SHIFT		0
#define  EEPROM_DEFAULT_CLOCK_PERIOD	0x60
#define  EEPROM_CHIP_SIZE		(64 * 1024)
#define GRC_EEPROM_DATA			0x0000683c
#define GRC_EEPROM_CTRL			0x00006840
#define GRC_MDI_CTRL			0x00006844
#define GRC_SEEPROM_DELAY		0x00006848
/* 0x684c --> 0x6890 unused */
#define GRC_VCPU_EXT_CTRL		0x00006890
#define GRC_VCPU_EXT_CTRL_HALT_CPU	 0x00400000
#define GRC_VCPU_EXT_CTRL_DISABLE_WOL	 0x20000000
#define GRC_FASTBOOT_PC			0x00006894	/* 5752, 5755, 5787 */

#define TG3_EAV_REF_CLCK_LSB		0x00006900
#define TG3_EAV_REF_CLCK_MSB		0x00006904
#define TG3_EAV_REF_CLCK_CTL		0x00006908
#define  TG3_EAV_REF_CLCK_CTL_STOP	 0x00000002
#define  TG3_EAV_REF_CLCK_CTL_RESUME	 0x00000004
#define  TG3_EAV_CTL_TSYNC_GPIO_MASK	 (0x3 << 16)
#define  TG3_EAV_CTL_TSYNC_WDOG0	 (1 << 17)

#define TG3_EAV_WATCHDOG0_LSB		0x00006918
#define TG3_EAV_WATCHDOG0_MSB		0x0000691c
#define  TG3_EAV_WATCHDOG0_EN		 (1 << 31)
#define  TG3_EAV_WATCHDOG_MSB_MASK	0x7fffffff

#define TG3_EAV_REF_CLK_CORRECT_CTL	0x00006928
#define  TG3_EAV_REF_CLK_CORRECT_EN	 (1 << 31)
#define  TG3_EAV_REF_CLK_CORRECT_NEG	 (1 << 30)

#define TG3_EAV_REF_CLK_CORRECT_MASK	0xffffff

/* 0x692c --> 0x7000 unused */

/* NVRAM Control registers */
#define NVRAM_CMD			0x00007000
#define  NVRAM_CMD_RESET		 0x00000001
#define  NVRAM_CMD_DONE			 0x00000008
#define  NVRAM_CMD_GO			 0x00000010
#define  NVRAM_CMD_WR			 0x00000020
#define  NVRAM_CMD_RD			 0x00000000
#define  NVRAM_CMD_ERASE		 0x00000040
#define  NVRAM_CMD_FIRST		 0x00000080
#define  NVRAM_CMD_LAST			 0x00000100
#define  NVRAM_CMD_WREN			 0x00010000
#define  NVRAM_CMD_WRDI			 0x00020000
#define NVRAM_STAT			0x00007004
#define NVRAM_WRDATA			0x00007008
#define NVRAM_ADDR			0x0000700c
#define  NVRAM_ADDR_MSK			0x07ffffff
#define NVRAM_RDDATA			0x00007010
#define NVRAM_CFG1			0x00007014
#define  NVRAM_CFG1_FLASHIF_ENAB	 0x00000001
#define  NVRAM_CFG1_BUFFERED_MODE	 0x00000002
#define  NVRAM_CFG1_PASS_THRU		 0x00000004
#define  NVRAM_CFG1_STATUS_BITS		 0x00000070
#define  NVRAM_CFG1_BIT_BANG		 0x00000008
#define  NVRAM_CFG1_FLASH_SIZE		 0x02000000
#define  NVRAM_CFG1_COMPAT_BYPASS	 0x80000000
#define  NVRAM_CFG1_VENDOR_MASK		 0x03000003
#define  FLASH_VENDOR_ATMEL_EEPROM	 0x02000000
#define  FLASH_VENDOR_ATMEL_FLASH_BUFFERED	 0x02000003
#define  FLASH_VENDOR_ATMEL_FLASH_UNBUFFERED	 0x00000003
#define  FLASH_VENDOR_ST			 0x03000001
#define  FLASH_VENDOR_SAIFUN		 0x01000003
#define  FLASH_VENDOR_SST_SMALL		 0x00000001
#define  FLASH_VENDOR_SST_LARGE		 0x02000001
#define  NVRAM_CFG1_5752VENDOR_MASK	 0x03c00003
#define  NVRAM_CFG1_5762VENDOR_MASK	 0x03e00003
#define  FLASH_5752VENDOR_ATMEL_EEPROM_64KHZ	 0x00000000
#define  FLASH_5752VENDOR_ATMEL_EEPROM_376KHZ	 0x02000000
#define  FLASH_5752VENDOR_ATMEL_FLASH_BUFFERED	 0x02000003
#define  FLASH_5752VENDOR_ST_M45PE10	 0x02400000
#define  FLASH_5752VENDOR_ST_M45PE20	 0x02400002
#define  FLASH_5752VENDOR_ST_M45PE40	 0x02400001
#define  FLASH_5755VENDOR_ATMEL_FLASH_1	 0x03400001
#define  FLASH_5755VENDOR_ATMEL_FLASH_2	 0x03400002
#define  FLASH_5755VENDOR_ATMEL_FLASH_3	 0x03400000
#define  FLASH_5755VENDOR_ATMEL_FLASH_4	 0x00000003
#define  FLASH_5755VENDOR_ATMEL_FLASH_5	 0x02000003
#define  FLASH_5755VENDOR_ATMEL_EEPROM_64KHZ	 0x03c00003
#define  FLASH_5755VENDOR_ATMEL_EEPROM_376KHZ	 0x03c00002
#define  FLASH_5787VENDOR_ATMEL_EEPROM_64KHZ	 0x03000003
#define  FLASH_5787VENDOR_ATMEL_EEPROM_376KHZ	 0x03000002
#define  FLASH_5787VENDOR_MICRO_EEPROM_64KHZ	 0x03000000
#define  FLASH_5787VENDOR_MICRO_EEPROM_376KHZ	 0x02000000
#define  FLASH_5761VENDOR_ATMEL_MDB021D	 0x00800003
#define  FLASH_5761VENDOR_ATMEL_MDB041D	 0x00800000
#define  FLASH_5761VENDOR_ATMEL_MDB081D	 0x00800002
#define  FLASH_5761VENDOR_ATMEL_MDB161D	 0x00800001
#define  FLASH_5761VENDOR_ATMEL_ADB021D	 0x00000003
#define  FLASH_5761VENDOR_ATMEL_ADB041D	 0x00000000
#define  FLASH_5761VENDOR_ATMEL_ADB081D	 0x00000002
#define  FLASH_5761VENDOR_ATMEL_ADB161D	 0x00000001
#define  FLASH_5761VENDOR_ST_M_M45PE20	 0x02800001
#define  FLASH_5761VENDOR_ST_M_M45PE40	 0x02800000
#define  FLASH_5761VENDOR_ST_M_M45PE80	 0x02800002
#define  FLASH_5761VENDOR_ST_M_M45PE16	 0x02800003
#define  FLASH_5761VENDOR_ST_A_M45PE20	 0x02000001
#define  FLASH_5761VENDOR_ST_A_M45PE40	 0x02000000
#define  FLASH_5761VENDOR_ST_A_M45PE80	 0x02000002
#define  FLASH_5761VENDOR_ST_A_M45PE16	 0x02000003
#define  FLASH_57780VENDOR_ATMEL_AT45DB011D 0x00400000
#define  FLASH_57780VENDOR_ATMEL_AT45DB011B 0x03400000
#define  FLASH_57780VENDOR_ATMEL_AT45DB021D 0x00400002
#define  FLASH_57780VENDOR_ATMEL_AT45DB021B 0x03400002
#define  FLASH_57780VENDOR_ATMEL_AT45DB041D 0x00400001
#define  FLASH_57780VENDOR_ATMEL_AT45DB041B 0x03400001
#define  FLASH_5717VENDOR_ATMEL_EEPROM	 0x02000001
#define  FLASH_5717VENDOR_MICRO_EEPROM	 0x02000003
#define  FLASH_5717VENDOR_ATMEL_MDB011D	 0x01000001
#define  FLASH_5717VENDOR_ATMEL_MDB021D	 0x01000003
#define  FLASH_5717VENDOR_ST_M_M25PE10	 0x02000000
#define  FLASH_5717VENDOR_ST_M_M25PE20	 0x02000002
#define  FLASH_5717VENDOR_ST_M_M45PE10	 0x00000001
#define  FLASH_5717VENDOR_ST_M_M45PE20	 0x00000003
#define  FLASH_5717VENDOR_ATMEL_ADB011B	 0x01400000
#define  FLASH_5717VENDOR_ATMEL_ADB021B	 0x01400002
#define  FLASH_5717VENDOR_ATMEL_ADB011D	 0x01400001
#define  FLASH_5717VENDOR_ATMEL_ADB021D	 0x01400003
#define  FLASH_5717VENDOR_ST_A_M25PE10	 0x02400000
#define  FLASH_5717VENDOR_ST_A_M25PE20	 0x02400002
#define  FLASH_5717VENDOR_ST_A_M45PE10	 0x02400001
#define  FLASH_5717VENDOR_ST_A_M45PE20	 0x02400003
#define  FLASH_5717VENDOR_ATMEL_45USPT	 0x03400000
#define  FLASH_5717VENDOR_ST_25USPT	 0x03400002
#define  FLASH_5717VENDOR_ST_45USPT	 0x03400001
#define  FLASH_5720_EEPROM_HD		 0x00000001
#define  FLASH_5720_EEPROM_LD		 0x00000003
#define  FLASH_5762_EEPROM_HD		 0x02000001
#define  FLASH_5762_EEPROM_LD		 0x02000003
#define  FLASH_5762_MX25L_100           0x00800000
#define  FLASH_5762_MX25L_200           0x00800002
#define  FLASH_5762_MX25L_400           0x00800001
#define  FLASH_5762_MX25L_800           0x00800003
#define  FLASH_5762_MX25L_160_320       0x03800002
#define  FLASH_5720VENDOR_M_ATMEL_DB011D 0x01000000
#define  FLASH_5720VENDOR_M_ATMEL_DB021D 0x01000002
#define  FLASH_5720VENDOR_M_ATMEL_DB041D 0x01000001
#define  FLASH_5720VENDOR_M_ATMEL_DB081D 0x01000003
#define  FLASH_5720VENDOR_M_ST_M25PE10	 0x02000000
#define  FLASH_5720VENDOR_M_ST_M25PE20	 0x02000002
#define  FLASH_5720VENDOR_M_ST_M25PE40	 0x02000001
#define  FLASH_5720VENDOR_M_ST_M25PE80	 0x02000003
#define  FLASH_5720VENDOR_M_ST_M45PE10	 0x03000000
#define  FLASH_5720VENDOR_M_ST_M45PE20	 0x03000002
#define  FLASH_5720VENDOR_M_ST_M45PE40	 0x03000001
#define  FLASH_5720VENDOR_M_ST_M45PE80	 0x03000003
#define  FLASH_5720VENDOR_A_ATMEL_DB011B 0x01800000
#define  FLASH_5720VENDOR_A_ATMEL_DB021B 0x01800002
#define  FLASH_5720VENDOR_A_ATMEL_DB041B 0x01800001
#define  FLASH_5720VENDOR_A_ATMEL_DB011D 0x01c00000
#define  FLASH_5720VENDOR_A_ATMEL_DB021D 0x01c00002
#define  FLASH_5720VENDOR_A_ATMEL_DB041D 0x01c00001
#define  FLASH_5720VENDOR_A_ATMEL_DB081D 0x01c00003
#define  FLASH_5720VENDOR_A_ST_M25PE10	 0x02800000
#define  FLASH_5720VENDOR_A_ST_M25PE20	 0x02800002
#define  FLASH_5720VENDOR_A_ST_M25PE40	 0x02800001
#define  FLASH_5720VENDOR_A_ST_M25PE80	 0x02800003
#define  FLASH_5720VENDOR_A_ST_M45PE10	 0x02c00000
#define  FLASH_5720VENDOR_A_ST_M45PE20	 0x02c00002
#define  FLASH_5720VENDOR_A_ST_M45PE40	 0x02c00001
#define  FLASH_5720VENDOR_A_ST_M45PE80	 0x02c00003
#define  FLASH_5720VENDOR_ATMEL_45USPT	 0x03c00000
#define  FLASH_5720VENDOR_ST_25USPT	 0x03c00002
#define  FLASH_5720VENDOR_ST_45USPT	 0x03c00001
#define  NVRAM_CFG1_5752PAGE_SIZE_MASK	 0x70000000
#define  FLASH_5752PAGE_SIZE_256	 0x00000000
#define  FLASH_5752PAGE_SIZE_512	 0x10000000
#define  FLASH_5752PAGE_SIZE_1K		 0x20000000
#define  FLASH_5752PAGE_SIZE_2K		 0x30000000
#define  FLASH_5752PAGE_SIZE_4K		 0x40000000
#define  FLASH_5752PAGE_SIZE_264	 0x50000000
#define  FLASH_5752PAGE_SIZE_528	 0x60000000
#define NVRAM_CFG2			0x00007018
#define NVRAM_CFG3			0x0000701c
#define NVRAM_SWARB			0x00007020
#define  SWARB_REQ_SET0			 0x00000001
#define  SWARB_REQ_SET1			 0x00000002
#define  SWARB_REQ_SET2			 0x00000004
#define  SWARB_REQ_SET3			 0x00000008
#define  SWARB_REQ_CLR0			 0x00000010
#define  SWARB_REQ_CLR1			 0x00000020
#define  SWARB_REQ_CLR2			 0x00000040
#define  SWARB_REQ_CLR3			 0x00000080
#define  SWARB_GNT0			 0x00000100
#define  SWARB_GNT1			 0x00000200
#define  SWARB_GNT2			 0x00000400
#define  SWARB_GNT3			 0x00000800
#define  SWARB_REQ0			 0x00001000
#define  SWARB_REQ1			 0x00002000
#define  SWARB_REQ2			 0x00004000
#define  SWARB_REQ3			 0x00008000
#define NVRAM_ACCESS			0x00007024
#define  ACCESS_ENABLE			 0x00000001
#define  ACCESS_WR_ENABLE		 0x00000002
#define NVRAM_WRITE1			0x00007028
/* 0x702c unused */

#define NVRAM_ADDR_LOCKOUT		0x00007030
#define NVRAM_AUTOSENSE_STATUS         0x00007038
#define AUTOSENSE_DEVID                        0x00000010
#define AUTOSENSE_DEVID_MASK           0x00000007
#define AUTOSENSE_SIZE_IN_MB           17
/* 0x703c --> 0x7500 unused */

#define OTP_MODE			0x00007500
#define OTP_MODE_OTP_THRU_GRC		 0x00000001
#define OTP_CTRL			0x00007504
#define OTP_CTRL_OTP_PROG_ENABLE	 0x00200000
#define OTP_CTRL_OTP_CMD_READ		 0x00000000
#define OTP_CTRL_OTP_CMD_INIT		 0x00000008
#define OTP_CTRL_OTP_CMD_START		 0x00000001
#define OTP_STATUS			0x00007508
#define OTP_STATUS_CMD_DONE		 0x00000001
#define OTP_ADDRESS			0x0000750c
#define OTP_ADDRESS_MAGIC1		 0x000000a0
#define OTP_ADDRESS_MAGIC2		 0x00000080
/* 0x7510 unused */

#define OTP_READ_DATA			0x00007514
/* 0x7518 --> 0x7c04 unused */

#define PCIE_TRANSACTION_CFG		0x00007c04
#define PCIE_TRANS_CFG_1SHOT_MSI	 0x20000000
#define PCIE_TRANS_CFG_LOM		 0x00000020
/* 0x7c08 --> 0x7d28 unused */

#define PCIE_PWR_MGMT_THRESH		0x00007d28
#define PCIE_PWR_MGMT_L1_THRESH_MSK	 0x0000ff00
#define PCIE_PWR_MGMT_L1_THRESH_4MS	 0x0000ff00
#define PCIE_PWR_MGMT_EXT_ASPM_TMR_EN	 0x01000000
/* 0x7d2c --> 0x7d54 unused */

#define TG3_PCIE_LNKCTL			0x00007d54
#define  TG3_PCIE_LNKCTL_L1_PLL_PD_EN	 0x00000008
#define  TG3_PCIE_LNKCTL_L1_PLL_PD_DIS	 0x00000080
/* 0x7d58 --> 0x7e70 unused */

#define TG3_PCIE_PHY_TSTCTL		0x00007e2c
#define  TG3_PCIE_PHY_TSTCTL_PCIE10	 0x00000040
#define  TG3_PCIE_PHY_TSTCTL_PSCRAM	 0x00000020

#define TG3_PCIE_EIDLE_DELAY		0x00007e70
#define  TG3_PCIE_EIDLE_DELAY_MASK	 0x0000001f
#define  TG3_PCIE_EIDLE_DELAY_13_CLKS	 0x0000000c
/* 0x7e74 --> 0x8000 unused */


/* Alternate PCIE definitions */
#define TG3_PCIE_TLDLPL_PORT		0x00007c00
#define TG3_PCIE_DL_LO_FTSMAX		0x0000000c
#define TG3_PCIE_DL_LO_FTSMAX_MSK	0x000000ff
#define TG3_PCIE_DL_LO_FTSMAX_VAL	0x0000002c
#define TG3_PCIE_PL_LO_PHYCTL1		 0x00000004
#define TG3_PCIE_PL_LO_PHYCTL1_L1PLLPD_EN	  0x00001000
#define TG3_PCIE_PL_LO_PHYCTL5		 0x00000014
#define TG3_PCIE_PL_LO_PHYCTL5_DIS_L2CLKREQ	  0x80000000

#define TG3_REG_BLK_SIZE		0x00008000

/* OTP bit definitions */
#define TG3_OTP_AGCTGT_MASK		0x000000e0
#define TG3_OTP_AGCTGT_SHIFT		1
#define TG3_OTP_HPFFLTR_MASK		0x00000300
#define TG3_OTP_HPFFLTR_SHIFT		1
#define TG3_OTP_HPFOVER_MASK		0x00000400
#define TG3_OTP_HPFOVER_SHIFT		1
#define TG3_OTP_LPFDIS_MASK		0x00000800
#define TG3_OTP_LPFDIS_SHIFT		11
#define TG3_OTP_VDAC_MASK		0xff000000
#define TG3_OTP_VDAC_SHIFT		24
#define TG3_OTP_10BTAMP_MASK		0x0000f000
#define TG3_OTP_10BTAMP_SHIFT		8
#define TG3_OTP_ROFF_MASK		0x00e00000
#define TG3_OTP_ROFF_SHIFT		11
#define TG3_OTP_RCOFF_MASK		0x001c0000
#define TG3_OTP_RCOFF_SHIFT		16

#define TG3_OTP_DEFAULT			0x286c1640


/* Hardware Legacy NVRAM layout */
#define TG3_NVM_VPD_OFF			0x100
#define TG3_NVM_VPD_LEN			256

/* Hardware Selfboot NVRAM layout */
#define TG3_NVM_HWSB_CFG1		0x00000004
#define  TG3_NVM_HWSB_CFG1_MAJMSK	0xf8000000
#define  TG3_NVM_HWSB_CFG1_MAJSFT	27
#define  TG3_NVM_HWSB_CFG1_MINMSK	0x07c00000
#define  TG3_NVM_HWSB_CFG1_MINSFT	22

#define TG3_EEPROM_MAGIC		0x669955aa
#define TG3_EEPROM_MAGIC_FW		0xa5000000
#define TG3_EEPROM_MAGIC_FW_MSK		0xff000000
#define TG3_EEPROM_SB_FORMAT_MASK	0x00e00000
#define TG3_EEPROM_SB_FORMAT_1		0x00200000
#define TG3_EEPROM_SB_REVISION_MASK	0x001f0000
#define TG3_EEPROM_SB_REVISION_0	0x00000000
#define TG3_EEPROM_SB_REVISION_2	0x00020000
#define TG3_EEPROM_SB_REVISION_3	0x00030000
#define TG3_EEPROM_SB_REVISION_4	0x00040000
#define TG3_EEPROM_SB_REVISION_5	0x00050000
#define TG3_EEPROM_SB_REVISION_6	0x00060000
#define TG3_EEPROM_MAGIC_HW		0xabcd
#define TG3_EEPROM_MAGIC_HW_MSK		0xffff

#define TG3_NVM_DIR_START		0x18
#define TG3_NVM_DIR_END			0x78
#define TG3_NVM_DIRENT_SIZE		0xc
#define TG3_NVM_DIRTYPE_SHIFT		24
#define TG3_NVM_DIRTYPE_LENMSK		0x003fffff
#define TG3_NVM_DIRTYPE_ASFINI		1
#define TG3_NVM_DIRTYPE_EXTVPD		20
#define TG3_NVM_PTREV_BCVER		0x94
#define TG3_NVM_BCVER_MAJMSK		0x0000ff00
#define TG3_NVM_BCVER_MAJSFT		8
#define TG3_NVM_BCVER_MINMSK		0x000000ff

#define TG3_EEPROM_SB_F1R0_EDH_OFF	0x10
#define TG3_EEPROM_SB_F1R2_EDH_OFF	0x14
#define TG3_EEPROM_SB_F1R2_MBA_OFF	0x10
#define TG3_EEPROM_SB_F1R3_EDH_OFF	0x18
#define TG3_EEPROM_SB_F1R4_EDH_OFF	0x1c
#define TG3_EEPROM_SB_F1R5_EDH_OFF	0x20
#define TG3_EEPROM_SB_F1R6_EDH_OFF	0x4c
#define TG3_EEPROM_SB_EDH_MAJ_MASK	0x00000700
#define TG3_EEPROM_SB_EDH_MAJ_SHFT	8
#define TG3_EEPROM_SB_EDH_MIN_MASK	0x000000ff
#define TG3_EEPROM_SB_EDH_BLD_MASK	0x0000f800
#define TG3_EEPROM_SB_EDH_BLD_SHFT	11


/* 32K Window into NIC internal memory */
#define NIC_SRAM_WIN_BASE		0x00008000

/* Offsets into first 32k of NIC internal memory. */
#define NIC_SRAM_PAGE_ZERO		0x00000000
#define NIC_SRAM_SEND_RCB		0x00000100 /* 16 * TG3_BDINFO_... */
#define NIC_SRAM_RCV_RET_RCB		0x00000200 /* 16 * TG3_BDINFO_... */
#define NIC_SRAM_STATS_BLK		0x00000300
#define NIC_SRAM_STATUS_BLK		0x00000b00

#define NIC_SRAM_FIRMWARE_MBOX		0x00000b50
#define  NIC_SRAM_FIRMWARE_MBOX_MAGIC1	 0x4B657654
#define  NIC_SRAM_FIRMWARE_MBOX_MAGIC2	 0x4861764b /* !dma on linkchg */

#define NIC_SRAM_DATA_SIG		0x00000b54
#define  NIC_SRAM_DATA_SIG_MAGIC	 0x4b657654 /* ascii for 'KevT' */

#define NIC_SRAM_DATA_CFG			0x00000b58
#define  NIC_SRAM_DATA_CFG_LED_MODE_MASK	 0x0000000c
#define  NIC_SRAM_DATA_CFG_LED_MODE_MAC		 0x00000000
#define  NIC_SRAM_DATA_CFG_LED_MODE_PHY_1	 0x00000004
#define  NIC_SRAM_DATA_CFG_LED_MODE_PHY_2	 0x00000008
#define  NIC_SRAM_DATA_CFG_PHY_TYPE_MASK	 0x00000030
#define  NIC_SRAM_DATA_CFG_PHY_TYPE_UNKNOWN	 0x00000000
#define  NIC_SRAM_DATA_CFG_PHY_TYPE_COPPER	 0x00000010
#define  NIC_SRAM_DATA_CFG_PHY_TYPE_FIBER	 0x00000020
#define  NIC_SRAM_DATA_CFG_WOL_ENABLE		 0x00000040
#define  NIC_SRAM_DATA_CFG_ASF_ENABLE		 0x00000080
#define  NIC_SRAM_DATA_CFG_EEPROM_WP		 0x00000100
#define  NIC_SRAM_DATA_CFG_MINI_PCI		 0x00001000
#define  NIC_SRAM_DATA_CFG_FIBER_WOL		 0x00004000
#define  NIC_SRAM_DATA_CFG_NO_GPIO2		 0x00100000
#define  NIC_SRAM_DATA_CFG_APE_ENABLE		 0x00200000

#define NIC_SRAM_DATA_VER			0x00000b5c
#define  NIC_SRAM_DATA_VER_SHIFT		 16

#define NIC_SRAM_DATA_PHY_ID		0x00000b74
#define  NIC_SRAM_DATA_PHY_ID1_MASK	 0xffff0000
#define  NIC_SRAM_DATA_PHY_ID2_MASK	 0x0000ffff

#define NIC_SRAM_FW_CMD_MBOX		0x00000b78
#define  FWCMD_NICDRV_ALIVE		 0x00000001
#define  FWCMD_NICDRV_PAUSE_FW		 0x00000002
#define  FWCMD_NICDRV_IPV4ADDR_CHG	 0x00000003
#define  FWCMD_NICDRV_IPV6ADDR_CHG	 0x00000004
#define  FWCMD_NICDRV_FIX_DMAR		 0x00000005
#define  FWCMD_NICDRV_FIX_DMAW		 0x00000006
#define  FWCMD_NICDRV_LINK_UPDATE	 0x0000000c
#define  FWCMD_NICDRV_ALIVE2		 0x0000000d
#define  FWCMD_NICDRV_ALIVE3		 0x0000000e
#define NIC_SRAM_FW_CMD_LEN_MBOX	0x00000b7c
#define NIC_SRAM_FW_CMD_DATA_MBOX	0x00000b80
#define NIC_SRAM_FW_ASF_STATUS_MBOX	0x00000c00
#define NIC_SRAM_FW_DRV_STATE_MBOX	0x00000c04
#define  DRV_STATE_START		 0x00000001
#define  DRV_STATE_START_DONE		 0x80000001
#define  DRV_STATE_UNLOAD		 0x00000002
#define  DRV_STATE_UNLOAD_DONE		 0x80000002
#define  DRV_STATE_WOL			 0x00000003
#define  DRV_STATE_SUSPEND		 0x00000004

#define NIC_SRAM_FW_RESET_TYPE_MBOX	0x00000c08

#define NIC_SRAM_MAC_ADDR_HIGH_MBOX	0x00000c14
#define NIC_SRAM_MAC_ADDR_LOW_MBOX	0x00000c18

#define NIC_SRAM_WOL_MBOX		0x00000d30
#define  WOL_SIGNATURE			 0x474c0000
#define  WOL_DRV_STATE_SHUTDOWN		 0x00000001
#define  WOL_DRV_WOL			 0x00000002
#define  WOL_SET_MAGIC_PKT		 0x00000004

#define NIC_SRAM_DATA_CFG_2		0x00000d38

#define  NIC_SRAM_DATA_CFG_2_APD_EN	 0x00004000
#define  SHASTA_EXT_LED_MODE_MASK	 0x00018000
#define  SHASTA_EXT_LED_LEGACY		 0x00000000
#define  SHASTA_EXT_LED_SHARED		 0x00008000
#define  SHASTA_EXT_LED_MAC		 0x00010000
#define  SHASTA_EXT_LED_COMBO		 0x00018000

#define NIC_SRAM_DATA_CFG_3		0x00000d3c
#define  NIC_SRAM_ASPM_DEBOUNCE		 0x00000002
#define  NIC_SRAM_LNK_FLAP_AVOID	 0x00400000
#define  NIC_SRAM_1G_ON_VAUX_OK		 0x00800000

#define NIC_SRAM_DATA_CFG_4		0x00000d60
#define  NIC_SRAM_GMII_MODE		 0x00000002
#define  NIC_SRAM_RGMII_INBAND_DISABLE	 0x00000004
#define  NIC_SRAM_RGMII_EXT_IBND_RX_EN	 0x00000008
#define  NIC_SRAM_RGMII_EXT_IBND_TX_EN	 0x00000010

#define NIC_SRAM_CPMU_STATUS		0x00000e00
#define  NIC_SRAM_CPMUSTAT_SIG		0x0000362c
#define  NIC_SRAM_CPMUSTAT_SIG_MSK	0x0000ffff

#define NIC_SRAM_DATA_CFG_5		0x00000e0c
#define  NIC_SRAM_DISABLE_1G_HALF_ADV	0x00000002

#define NIC_SRAM_RX_MINI_BUFFER_DESC	0x00001000

#define NIC_SRAM_DMA_DESC_POOL_BASE	0x00002000
#define  NIC_SRAM_DMA_DESC_POOL_SIZE	 0x00002000
#define NIC_SRAM_TX_BUFFER_DESC		0x00004000 /* 512 entries */
#define NIC_SRAM_RX_BUFFER_DESC		0x00006000 /* 256 entries */
#define NIC_SRAM_RX_JUMBO_BUFFER_DESC	0x00007000 /* 256 entries */
#define NIC_SRAM_MBUF_POOL_BASE		0x00008000
#define  NIC_SRAM_MBUF_POOL_SIZE96	 0x00018000
#define  NIC_SRAM_MBUF_POOL_SIZE64	 0x00010000
#define  NIC_SRAM_MBUF_POOL_BASE5705	0x00010000
#define  NIC_SRAM_MBUF_POOL_SIZE5705	0x0000e000

#define TG3_SRAM_RXCPU_SCRATCH_BASE_57766	0x00030000
#define  TG3_SRAM_RXCPU_SCRATCH_SIZE_57766	 0x00010000
#define TG3_57766_FW_BASE_ADDR			0x00030000
#define TG3_57766_FW_HANDSHAKE			0x0003fccc
#define TG3_SBROM_IN_SERVICE_LOOP		0x51

#define TG3_SRAM_RX_STD_BDCACHE_SIZE_5700	128
#define TG3_SRAM_RX_STD_BDCACHE_SIZE_5755	64
#define TG3_SRAM_RX_STD_BDCACHE_SIZE_5906	32

#define TG3_SRAM_RX_JMB_BDCACHE_SIZE_5700	64
#define TG3_SRAM_RX_JMB_BDCACHE_SIZE_5717	16


/* Currently this is fixed. */
#define TG3_PHY_MII_ADDR		0x01


/*** Tigon3 specific PHY MII registers. ***/
#define MII_TG3_MMD_CTRL		0x0d /* MMD Access Control register */
#define MII_TG3_MMD_CTRL_DATA_NOINC	0x4000
#define MII_TG3_MMD_ADDRESS		0x0e /* MMD Address Data register */

#define MII_TG3_EXT_CTRL		0x10 /* Extended control register */
#define  MII_TG3_EXT_CTRL_FIFO_ELASTIC	0x0001
#define  MII_TG3_EXT_CTRL_LNK3_LED_MODE	0x0002
#define  MII_TG3_EXT_CTRL_FORCE_LED_OFF	0x0008
#define  MII_TG3_EXT_CTRL_TBI		0x8000

#define MII_TG3_EXT_STAT		0x11 /* Extended status register */
#define  MII_TG3_EXT_STAT_MDIX		0x2000
#define  MII_TG3_EXT_STAT_LPASS		0x0100

#define MII_TG3_RXR_COUNTERS		0x14 /* Local/Remote Receiver Counts */
#define MII_TG3_DSP_RW_PORT		0x15 /* DSP coefficient read/write port */
#define MII_TG3_DSP_CONTROL		0x16 /* DSP control register */
#define MII_TG3_DSP_ADDRESS		0x17 /* DSP address register */

#define MII_TG3_DSP_TAP1		0x0001
#define  MII_TG3_DSP_TAP1_AGCTGT_DFLT	0x0007
#define MII_TG3_DSP_TAP26		0x001a
#define  MII_TG3_DSP_TAP26_ALNOKO	0x0001
#define  MII_TG3_DSP_TAP26_RMRXSTO	0x0002
#define  MII_TG3_DSP_TAP26_OPCSINPT	0x0004
#define MII_TG3_DSP_AADJ1CH0		0x001f
#define MII_TG3_DSP_CH34TP2		0x4022
#define MII_TG3_DSP_CH34TP2_HIBW01	0x01ff
#define MII_TG3_DSP_AADJ1CH3		0x601f
#define  MII_TG3_DSP_AADJ1CH3_ADCCKADJ	0x0002
#define MII_TG3_DSP_EXP1_INT_STAT	0x0f01
#define MII_TG3_DSP_EXP8		0x0f08
#define  MII_TG3_DSP_EXP8_REJ2MHz	0x0001
#define  MII_TG3_DSP_EXP8_AEDW		0x0200
#define MII_TG3_DSP_EXP75		0x0f75
#define MII_TG3_DSP_EXP96		0x0f96
#define MII_TG3_DSP_EXP97		0x0f97

#define MII_TG3_AUX_CTRL		0x18 /* auxiliary control register */

#define MII_TG3_AUXCTL_SHDWSEL_AUXCTL	0x0000
#define MII_TG3_AUXCTL_ACTL_TX_6DB	0x0400
#define MII_TG3_AUXCTL_ACTL_SMDSP_ENA	0x0800
#define MII_TG3_AUXCTL_ACTL_EXTPKTLEN	0x4000
#define MII_TG3_AUXCTL_ACTL_EXTLOOPBK	0x8000

#define MII_TG3_AUXCTL_SHDWSEL_PWRCTL	0x0002
#define MII_TG3_AUXCTL_PCTL_WOL_EN	0x0008
#define MII_TG3_AUXCTL_PCTL_100TX_LPWR	0x0010
#define MII_TG3_AUXCTL_PCTL_SPR_ISOLATE	0x0020
#define MII_TG3_AUXCTL_PCTL_CL_AB_TXDAC	0x0040
#define MII_TG3_AUXCTL_PCTL_VREG_11V	0x0180

#define MII_TG3_AUXCTL_SHDWSEL_MISCTEST	0x0004

#define MII_TG3_AUXCTL_SHDWSEL_MISC	0x0007
#define MII_TG3_AUXCTL_MISC_WIRESPD_EN	0x0010
#define MII_TG3_AUXCTL_MISC_FORCE_AMDIX	0x0200
#define MII_TG3_AUXCTL_MISC_RDSEL_SHIFT	12
#define MII_TG3_AUXCTL_MISC_WREN	0x8000


#define MII_TG3_AUX_STAT		0x19 /* auxiliary status register */
#define MII_TG3_AUX_STAT_LPASS		0x0004
#define MII_TG3_AUX_STAT_SPDMASK	0x0700
#define MII_TG3_AUX_STAT_10HALF		0x0100
#define MII_TG3_AUX_STAT_10FULL		0x0200
#define MII_TG3_AUX_STAT_100HALF	0x0300
#define MII_TG3_AUX_STAT_100_4		0x0400
#define MII_TG3_AUX_STAT_100FULL	0x0500
#define MII_TG3_AUX_STAT_1000HALF	0x0600
#define MII_TG3_AUX_STAT_1000FULL	0x0700
#define MII_TG3_AUX_STAT_100		0x0008
#define MII_TG3_AUX_STAT_FULL		0x0001

#define MII_TG3_ISTAT			0x1a /* IRQ status register */
#define MII_TG3_IMASK			0x1b /* IRQ mask register */

/* ISTAT/IMASK event bits */
#define MII_TG3_INT_LINKCHG		0x0002
#define MII_TG3_INT_SPEEDCHG		0x0004
#define MII_TG3_INT_DUPLEXCHG		0x0008
#define MII_TG3_INT_ANEG_PAGE_RX	0x0400

#define MII_TG3_MISC_SHDW		0x1c
#define MII_TG3_MISC_SHDW_WREN		0x8000

#define MII_TG3_MISC_SHDW_APD_WKTM_84MS	0x0001
#define MII_TG3_MISC_SHDW_APD_ENABLE	0x0020
#define MII_TG3_MISC_SHDW_APD_SEL	0x2800

#define MII_TG3_MISC_SHDW_SCR5_C125OE	0x0001
#define MII_TG3_MISC_SHDW_SCR5_DLLAPD	0x0002
#define MII_TG3_MISC_SHDW_SCR5_SDTL	0x0004
#define MII_TG3_MISC_SHDW_SCR5_DLPTLM	0x0008
#define MII_TG3_MISC_SHDW_SCR5_LPED	0x0010
#define MII_TG3_MISC_SHDW_SCR5_SEL	0x1400

#define MII_TG3_TEST1			0x1e
#define MII_TG3_TEST1_TRIM_EN		0x0010
#define MII_TG3_TEST1_CRC_EN		0x8000

/* Clause 45 expansion registers */
#define TG3_CL45_D7_EEERES_STAT		0x803e
#define TG3_CL45_D7_EEERES_STAT_LP_100TX	0x0002
#define TG3_CL45_D7_EEERES_STAT_LP_1000T	0x0004


/* Fast Ethernet Tranceiver definitions */
#define MII_TG3_FET_PTEST		0x17
#define  MII_TG3_FET_PTEST_TRIM_SEL	0x0010
#define  MII_TG3_FET_PTEST_TRIM_2	0x0002
#define  MII_TG3_FET_PTEST_FRC_TX_LINK	0x1000
#define  MII_TG3_FET_PTEST_FRC_TX_LOCK	0x0800

#define MII_TG3_FET_GEN_STAT		0x1c
#define  MII_TG3_FET_GEN_STAT_MDIXSTAT	0x2000

#define MII_TG3_FET_TEST		0x1f
#define  MII_TG3_FET_SHADOW_EN		0x0080

#define MII_TG3_FET_SHDW_MISCCTRL	0x10
#define  MII_TG3_FET_SHDW_MISCCTRL_MDIX	0x4000

#define MII_TG3_FET_SHDW_AUXMODE4	0x1a
#define MII_TG3_FET_SHDW_AUXMODE4_SBPD	0x0008

#define MII_TG3_FET_SHDW_AUXSTAT2	0x1b
#define  MII_TG3_FET_SHDW_AUXSTAT2_APD	0x0020

/* Serdes PHY Register Definitions */
#define SERDES_TG3_1000X_STATUS		0x14
#define  SERDES_TG3_SGMII_MODE		 0x0001
#define  SERDES_TG3_LINK_UP		 0x0002
#define  SERDES_TG3_FULL_DUPLEX		 0x0004
#define  SERDES_TG3_SPEED_100		 0x0008
#define  SERDES_TG3_SPEED_1000		 0x0010

/* APE registers.  Accessible through BAR1 */
#define TG3_APE_GPIO_MSG		0x0008
#define TG3_APE_GPIO_MSG_SHIFT		4
#define TG3_APE_EVENT			0x000c
#define  APE_EVENT_1			 0x00000001
#define TG3_APE_LOCK_REQ		0x002c
#define  APE_LOCK_REQ_DRIVER		 0x00001000
#define TG3_APE_LOCK_GRANT		0x004c
#define  APE_LOCK_GRANT_DRIVER		 0x00001000
#define TG3_APE_OTP_CTRL		0x00e8
#define  APE_OTP_CTRL_PROG_EN		 0x200000
#define  APE_OTP_CTRL_CMD_RD		 0x000000
#define  APE_OTP_CTRL_START		 0x000001
#define TG3_APE_OTP_STATUS		0x00ec
#define  APE_OTP_STATUS_CMD_DONE	 0x000001
#define TG3_APE_OTP_ADDR		0x00f0
#define  APE_OTP_ADDR_CPU_ENABLE	 0x80000000
#define TG3_APE_OTP_RD_DATA		0x00f8

#define OTP_ADDRESS_MAGIC0		 0x00000050
#define TG3_OTP_MAGIC0_VALID(val)		\
	((((val) & 0xf0000000) == 0xa0000000) ||\
	 (((val) & 0x0f000000) == 0x0a000000))

/* APE shared memory.  Accessible through BAR1 */
#define TG3_APE_SHMEM_BASE		0x4000
#define TG3_APE_SEG_SIG			0x4000
#define  APE_SEG_SIG_MAGIC		 0x41504521
#define TG3_APE_FW_STATUS		0x400c
#define  APE_FW_STATUS_READY		 0x00000100
#define TG3_APE_FW_FEATURES		0x4010
#define  TG3_APE_FW_FEATURE_NCSI	 0x00000002
#define TG3_APE_FW_VERSION		0x4018
#define  APE_FW_VERSION_MAJMSK		 0xff000000
#define  APE_FW_VERSION_MAJSFT		 24
#define  APE_FW_VERSION_MINMSK		 0x00ff0000
#define  APE_FW_VERSION_MINSFT		 16
#define  APE_FW_VERSION_REVMSK		 0x0000ff00
#define  APE_FW_VERSION_REVSFT		 8
#define  APE_FW_VERSION_BLDMSK		 0x000000ff
#define TG3_APE_SEG_MSG_BUF_OFF		0x401c
#define TG3_APE_SEG_MSG_BUF_LEN		0x4020
#define TG3_APE_HOST_SEG_SIG		0x4200
#define  APE_HOST_SEG_SIG_MAGIC		 0x484f5354
#define TG3_APE_HOST_SEG_LEN		0x4204
#define  APE_HOST_SEG_LEN_MAGIC		 0x00000020
#define TG3_APE_HOST_INIT_COUNT		0x4208
#define TG3_APE_HOST_DRIVER_ID		0x420c
#define  APE_HOST_DRIVER_ID_LINUX	 0xf0000000
#define  APE_HOST_DRIVER_ID_MAGIC(maj, min)	\
	(APE_HOST_DRIVER_ID_LINUX | (maj & 0xff) << 16 | (min & 0xff) << 8)
#define TG3_APE_HOST_BEHAVIOR		0x4210
#define  APE_HOST_BEHAV_NO_PHYLOCK	 0x00000001
#define TG3_APE_HOST_HEARTBEAT_INT_MS	0x4214
#define  APE_HOST_HEARTBEAT_INT_DISABLE	 0
#define  APE_HOST_HEARTBEAT_INT_5SEC	 5000
#define TG3_APE_HOST_HEARTBEAT_COUNT	0x4218
#define TG3_APE_HOST_DRVR_STATE		0x421c
#define TG3_APE_HOST_DRVR_STATE_START	 0x00000001
#define TG3_APE_HOST_DRVR_STATE_UNLOAD	 0x00000002
#define TG3_APE_HOST_DRVR_STATE_WOL	 0x00000003
#define TG3_APE_HOST_WOL_SPEED		0x4224
#define TG3_APE_HOST_WOL_SPEED_AUTO	 0x00008000

#define TG3_APE_EVENT_STATUS		0x4300

#define  APE_EVENT_STATUS_DRIVER_EVNT	 0x00000010
#define  APE_EVENT_STATUS_STATE_CHNGE	 0x00000500
#define  APE_EVENT_STATUS_SCRTCHPD_READ	 0x00001600
#define  APE_EVENT_STATUS_SCRTCHPD_WRITE 0x00001700
#define  APE_EVENT_STATUS_STATE_START	 0x00010000
#define  APE_EVENT_STATUS_STATE_UNLOAD	 0x00020000
#define  APE_EVENT_STATUS_STATE_WOL	 0x00030000
#define  APE_EVENT_STATUS_STATE_SUSPEND	 0x00040000
#define  APE_EVENT_STATUS_EVENT_PENDING	 0x80000000

#define TG3_APE_PER_LOCK_REQ		0x8400
#define  APE_LOCK_PER_REQ_DRIVER	 0x00001000
#define TG3_APE_PER_LOCK_GRANT		0x8420
#define  APE_PER_LOCK_GRANT_DRIVER	 0x00001000

/* APE convenience enumerations. */
#define TG3_APE_LOCK_PHY0		0
#define TG3_APE_LOCK_GRC		1
#define TG3_APE_LOCK_PHY1		2
#define TG3_APE_LOCK_PHY2		3
#define TG3_APE_LOCK_MEM		4
#define TG3_APE_LOCK_PHY3		5
#define TG3_APE_LOCK_GPIO		7

#define TG3_APE_HB_INTERVAL             (tp->ape_hb_interval)
#define TG3_EEPROM_SB_F1R2_MBA_OFF	0x10


/* There are two ways to manage the TX descriptors on the tigon3.
 * Either the descriptors are in host DMA'able memory, or they
 * exist only in the cards on-chip SRAM.  All 16 send bds are under
 * the same mode, they may not be configured individually.
 *
 * This driver always uses host memory TX descriptors.
 *
 * To use host memory TX descriptors:
 *	1) Set GRC_MODE_HOST_SENDBDS in GRC_MODE register.
 *	   Make sure GRC_MODE_4X_NIC_SEND_RINGS is clear.
 *	2) Allocate DMA'able memory.
 *	3) In NIC_SRAM_SEND_RCB (of desired index) of on-chip SRAM:
 *	   a) Set TG3_BDINFO_HOST_ADDR to DMA address of memory
 *	      obtained in step 2
 *	   b) Set TG3_BDINFO_NIC_ADDR to NIC_SRAM_TX_BUFFER_DESC.
 *	   c) Set len field of TG3_BDINFO_MAXLEN_FLAGS to number
 *            of TX descriptors.  Leave flags field clear.
 *	4) Access TX descriptors via host memory.  The chip
 *	   will refetch into local SRAM as needed when producer
 *	   index mailboxes are updated.
 *
 * To use on-chip TX descriptors:
 *	1) Set GRC_MODE_4X_NIC_SEND_RINGS in GRC_MODE register.
 *	   Make sure GRC_MODE_HOST_SENDBDS is clear.
 *	2) In NIC_SRAM_SEND_RCB (of desired index) of on-chip SRAM:
 *	   a) Set TG3_BDINFO_HOST_ADDR to zero.
 *	   b) Set TG3_BDINFO_NIC_ADDR to NIC_SRAM_TX_BUFFER_DESC
 *	   c) TG3_BDINFO_MAXLEN_FLAGS is don't care.
 *	3) Access TX descriptors directly in on-chip SRAM
 *	   using normal {read,write}l().  (and not using
 *         pointer dereferencing of ioremap()'d memory like
 *	   the broken Broadcom driver does)
 *
 * Note that BDINFO_FLAGS_DISABLED should be set in the flags field of
 * TG3_BDINFO_MAXLEN_FLAGS of all unused SEND_RCB indices.
 */
struct tg3_tx_buffer_desc {
	u32				addr_hi;
	u32				addr_lo;

	u32				len_flags;
#define TXD_FLAG_TCPUDP_CSUM		0x0001
#define TXD_FLAG_IP_CSUM		0x0002
#define TXD_FLAG_END			0x0004
#define TXD_FLAG_IP_FRAG		0x0008
#define TXD_FLAG_JMB_PKT		0x0008
#define TXD_FLAG_IP_FRAG_END		0x0010
#define TXD_FLAG_HWTSTAMP		0x0020
#define TXD_FLAG_VLAN			0x0040
#define TXD_FLAG_COAL_NOW		0x0080
#define TXD_FLAG_CPU_PRE_DMA		0x0100
#define TXD_FLAG_CPU_POST_DMA		0x0200
#define TXD_FLAG_ADD_SRC_ADDR		0x1000
#define TXD_FLAG_CHOOSE_SRC_ADDR	0x6000
#define TXD_FLAG_NO_CRC			0x8000
#define TXD_LEN_SHIFT			16

	u32				vlan_tag;
#define TXD_VLAN_TAG_SHIFT		0
#define TXD_MSS_SHIFT			16
};

#define TXD_ADDR			0x00UL /* 64-bit */
#define TXD_LEN_FLAGS			0x08UL /* 32-bit (upper 16-bits are len) */
#define TXD_VLAN_TAG			0x0cUL /* 32-bit (upper 16-bits are tag) */
#define TXD_SIZE			0x10UL

struct tg3_rx_buffer_desc {
	u32				addr_hi;
	u32				addr_lo;

	u32				idx_len;
#define RXD_IDX_MASK	0xffff0000
#define RXD_IDX_SHIFT	16
#define RXD_LEN_MASK	0x0000ffff
#define RXD_LEN_SHIFT	0

	u32				type_flags;
#define RXD_TYPE_SHIFT	16
#define RXD_FLAGS_SHIFT	0

#define RXD_FLAG_END			0x0004
#define RXD_FLAG_MINI			0x0800
#define RXD_FLAG_JUMBO			0x0020
#define RXD_FLAG_VLAN			0x0040
#define RXD_FLAG_ERROR			0x0400
#define RXD_FLAG_IP_CSUM		0x1000
#define RXD_FLAG_TCPUDP_CSUM		0x2000
#define RXD_FLAG_IS_TCP			0x4000
#define RXD_FLAG_PTPSTAT_MASK		0x0210
#define RXD_FLAG_PTPSTAT_PTPV1		0x0010
#define RXD_FLAG_PTPSTAT_PTPV2		0x0200

	u32				ip_tcp_csum;
#define RXD_IPCSUM_MASK		0xffff0000
#define RXD_IPCSUM_SHIFT	16
#define RXD_TCPCSUM_MASK	0x0000ffff
#define RXD_TCPCSUM_SHIFT	0

	u32				err_vlan;

#define RXD_VLAN_MASK			0x0000ffff

#define RXD_ERR_BAD_CRC			0x00010000
#define RXD_ERR_COLLISION		0x00020000
#define RXD_ERR_LINK_LOST		0x00040000
#define RXD_ERR_PHY_DECODE		0x00080000
#define RXD_ERR_ODD_NIBBLE_RCVD_MII	0x00100000
#define RXD_ERR_MAC_ABRT		0x00200000
#define RXD_ERR_TOO_SMALL		0x00400000
#define RXD_ERR_NO_RESOURCES		0x00800000
#define RXD_ERR_HUGE_FRAME		0x01000000

#define RXD_ERR_MASK	(RXD_ERR_BAD_CRC | RXD_ERR_COLLISION |		\
			 RXD_ERR_LINK_LOST | RXD_ERR_PHY_DECODE |	\
			 RXD_ERR_MAC_ABRT | RXD_ERR_TOO_SMALL |		\
			 RXD_ERR_NO_RESOURCES | RXD_ERR_HUGE_FRAME)

	u32				reserved;
	u32				opaque;
#define RXD_OPAQUE_INDEX_MASK		0x0000ffff
#define RXD_OPAQUE_INDEX_SHIFT		0
#define RXD_OPAQUE_RING_STD		0x00010000
#define RXD_OPAQUE_RING_JUMBO		0x00020000
#define RXD_OPAQUE_RING_MINI		0x00040000
#define RXD_OPAQUE_RING_MASK		0x00070000
};

struct tg3_ext_rx_buffer_desc {
	struct {
		u32			addr_hi;
		u32			addr_lo;
	}				addrlist[3];
	u32				len2_len1;
	u32				resv_len3;
	struct tg3_rx_buffer_desc	std;
};

/* We only use this when testing out the DMA engine
 * at probe time.  This is the internal format of buffer
 * descriptors used by the chip at NIC_SRAM_DMA_DESCS.
 */
struct tg3_internal_buffer_desc {
	u32				addr_hi;
	u32				addr_lo;
	u32				nic_mbuf;
	/* XXX FIX THIS */
#ifdef __BIG_ENDIAN
	u16				cqid_sqid;
	u16				len;
#else
	u16				len;
	u16				cqid_sqid;
#endif
	u32				flags;
	u32				__cookie1;
	u32				__cookie2;
	u32				__cookie3;
};

#define TG3_HW_STATUS_SIZE		0x50
struct tg3_hw_status {
	u32				status;
#define SD_STATUS_UPDATED		0x00000001
#define SD_STATUS_LINK_CHG		0x00000002
#define SD_STATUS_ERROR			0x00000004

	u32				status_tag;

#ifdef __BIG_ENDIAN
	u16				rx_consumer;
	u16				rx_jumbo_consumer;
#else
	u16				rx_jumbo_consumer;
	u16				rx_consumer;
#endif

#ifdef __BIG_ENDIAN
	u16				reserved;
	u16				rx_mini_consumer;
#else
	u16				rx_mini_consumer;
	u16				reserved;
#endif
	struct {
#ifdef __BIG_ENDIAN
		u16			tx_consumer;
		u16			rx_producer;
#else
		u16			rx_producer;
		u16			tx_consumer;
#endif
	}				idx[16];
};

typedef struct {
	u32 high, low;
} tg3_stat64_t;

struct tg3_hw_stats {
	u8				__reserved0[0x400-0x300];

	/* Statistics maintained by Receive MAC. */
	tg3_stat64_t			rx_octets;
	u64				__reserved1;
	tg3_stat64_t			rx_fragments;
	tg3_stat64_t			rx_ucast_packets;
	tg3_stat64_t			rx_mcast_packets;
	tg3_stat64_t			rx_bcast_packets;
	tg3_stat64_t			rx_fcs_errors;
	tg3_stat64_t			rx_align_errors;
	tg3_stat64_t			rx_xon_pause_rcvd;
	tg3_stat64_t			rx_xoff_pause_rcvd;
	tg3_stat64_t			rx_mac_ctrl_rcvd;
	tg3_stat64_t			rx_xoff_entered;
	tg3_stat64_t			rx_frame_too_long_errors;
	tg3_stat64_t			rx_jabbers;
	tg3_stat64_t			rx_undersize_packets;
	tg3_stat64_t			rx_in_length_errors;
	tg3_stat64_t			rx_out_length_errors;
	tg3_stat64_t			rx_64_or_less_octet_packets;
	tg3_stat64_t			rx_65_to_127_octet_packets;
	tg3_stat64_t			rx_128_to_255_octet_packets;
	tg3_stat64_t			rx_256_to_511_octet_packets;
	tg3_stat64_t			rx_512_to_1023_octet_packets;
	tg3_stat64_t			rx_1024_to_1522_octet_packets;
	tg3_stat64_t			rx_1523_to_2047_octet_packets;
	tg3_stat64_t			rx_2048_to_4095_octet_packets;
	tg3_stat64_t			rx_4096_to_8191_octet_packets;
	tg3_stat64_t			rx_8192_to_9022_octet_packets;

	u64				__unused0[37];

	/* Statistics maintained by Transmit MAC. */
	tg3_stat64_t			tx_octets;
	u64				__reserved2;
	tg3_stat64_t			tx_collisions;
	tg3_stat64_t			tx_xon_sent;
	tg3_stat64_t			tx_xoff_sent;
	tg3_stat64_t			tx_flow_control;
	tg3_stat64_t			tx_mac_errors;
	tg3_stat64_t			tx_single_collisions;
	tg3_stat64_t			tx_mult_collisions;
	tg3_stat64_t			tx_deferred;
	u64				__reserved3;
	tg3_stat64_t			tx_excessive_collisions;
	tg3_stat64_t			tx_late_collisions;
	tg3_stat64_t			tx_collide_2times;
	tg3_stat64_t			tx_collide_3times;
	tg3_stat64_t			tx_collide_4times;
	tg3_stat64_t			tx_collide_5times;
	tg3_stat64_t			tx_collide_6times;
	tg3_stat64_t			tx_collide_7times;
	tg3_stat64_t			tx_collide_8times;
	tg3_stat64_t			tx_collide_9times;
	tg3_stat64_t			tx_collide_10times;
	tg3_stat64_t			tx_collide_11times;
	tg3_stat64_t			tx_collide_12times;
	tg3_stat64_t			tx_collide_13times;
	tg3_stat64_t			tx_collide_14times;
	tg3_stat64_t			tx_collide_15times;
	tg3_stat64_t			tx_ucast_packets;
	tg3_stat64_t			tx_mcast_packets;
	tg3_stat64_t			tx_bcast_packets;
	tg3_stat64_t			tx_carrier_sense_errors;
	tg3_stat64_t			tx_discards;
	tg3_stat64_t			tx_errors;

	u64				__unused1[31];

	/* Statistics maintained by Receive List Placement. */
	tg3_stat64_t			COS_rx_packets[16];
	tg3_stat64_t			COS_rx_filter_dropped;
	tg3_stat64_t			dma_writeq_full;
	tg3_stat64_t			dma_write_prioq_full;
	tg3_stat64_t			rxbds_empty;
	tg3_stat64_t			rx_discards;
	tg3_stat64_t			rx_errors;
	tg3_stat64_t			rx_threshold_hit;

	u64				__unused2[9];

	/* Statistics maintained by Send Data Initiator. */
	tg3_stat64_t			COS_out_packets[16];
	tg3_stat64_t			dma_readq_full;
	tg3_stat64_t			dma_read_prioq_full;
	tg3_stat64_t			tx_comp_queue_full;

	/* Statistics maintained by Host Coalescing. */
	tg3_stat64_t			ring_set_send_prod_index;
	tg3_stat64_t			ring_status_update;
	tg3_stat64_t			nic_irqs;
	tg3_stat64_t			nic_avoided_irqs;
	tg3_stat64_t			nic_tx_threshold_hit;

	/* NOT a part of the hardware statistics block format.
	 * These stats are here as storage for tg3_periodic_fetch_stats().
	 */
	tg3_stat64_t			mbuf_lwm_thresh_hit;

	u8				__reserved4[0xb00-0x9c8];
};

#define TG3_SD_NUM_RECS			3
#define TG3_OCIR_LEN			(sizeof(struct tg3_ocir))
#define TG3_OCIR_SIG_MAGIC		0x5253434f
#define TG3_OCIR_FLAG_ACTIVE		0x00000001

#define TG3_TEMP_CAUTION_OFFSET		0xc8
#define TG3_TEMP_MAX_OFFSET		0xcc
#define TG3_TEMP_SENSOR_OFFSET		0xd4


struct tg3_ocir {
	u32				signature;
	u16				version_flags;
	u16				refresh_int;
	u32				refresh_tmr;
	u32				update_tmr;
	u32				dst_base_addr;
	u16				src_hdr_offset;
	u16				src_hdr_length;
	u16				src_data_offset;
	u16				src_data_length;
	u16				dst_hdr_offset;
	u16				dst_data_offset;
	u16				dst_reg_upd_offset;
	u16				dst_sem_offset;
	u32				reserved1[2];
	u32				port0_flags;
	u32				port1_flags;
	u32				port2_flags;
	u32				port3_flags;
	u32				reserved2[1];
};


/* 'mapping' is superfluous as the chip does not write into
 * the tx/rx post rings so we could just fetch it from there.
 * But the cache behavior is better how we are doing it now.
 *
 * This driver uses new build_skb() API :
 * RX ring buffer contains pointer to kmalloc() data only,
 * skb are built only after Hardware filled the frame.
 */
struct ring_info {
	u8				*data;
	DEFINE_DMA_UNMAP_ADDR(mapping);
};

struct tg3_tx_ring_info {
	struct sk_buff			*skb;
	DEFINE_DMA_UNMAP_ADDR(mapping);
	bool				fragmented;
};

struct tg3_link_config {
	/* Describes what we're trying to get. */
	u32				advertising;
	u32				speed;
	u8				duplex;
	u8				autoneg;
	u8				flowctrl;

	/* Describes what we actually have. */
	u8				active_flowctrl;

	u8				active_duplex;
	u32				active_speed;
	u32				rmt_adv;
};

struct tg3_bufmgr_config {
	u32		mbuf_read_dma_low_water;
	u32		mbuf_mac_rx_low_water;
	u32		mbuf_high_water;

	u32		mbuf_read_dma_low_water_jumbo;
	u32		mbuf_mac_rx_low_water_jumbo;
	u32		mbuf_high_water_jumbo;

	u32		dma_low_water;
	u32		dma_high_water;
};

struct tg3_ethtool_stats {
	/* Statistics maintained by Receive MAC. */
	u64		rx_octets;
	u64		rx_fragments;
	u64		rx_ucast_packets;
	u64		rx_mcast_packets;
	u64		rx_bcast_packets;
	u64		rx_fcs_errors;
	u64		rx_align_errors;
	u64		rx_xon_pause_rcvd;
	u64		rx_xoff_pause_rcvd;
	u64		rx_mac_ctrl_rcvd;
	u64		rx_xoff_entered;
	u64		rx_frame_too_long_errors;
	u64		rx_jabbers;
	u64		rx_undersize_packets;
	u64		rx_in_length_errors;
	u64		rx_out_length_errors;
	u64		rx_64_or_less_octet_packets;
	u64		rx_65_to_127_octet_packets;
	u64		rx_128_to_255_octet_packets;
	u64		rx_256_to_511_octet_packets;
	u64		rx_512_to_1023_octet_packets;
	u64		rx_1024_to_1522_octet_packets;
	u64		rx_1523_to_2047_octet_packets;
	u64		rx_2048_to_4095_octet_packets;
	u64		rx_4096_to_8191_octet_packets;
	u64		rx_8192_to_9022_octet_packets;

	/* Statistics maintained by Transmit MAC. */
	u64		tx_octets;
	u64		tx_collisions;
	u64		tx_xon_sent;
	u64		tx_xoff_sent;
	u64		tx_flow_control;
	u64		tx_mac_errors;
	u64		tx_single_collisions;
	u64		tx_mult_collisions;
	u64		tx_deferred;
	u64		tx_excessive_collisions;
	u64		tx_late_collisions;
	u64		tx_collide_2times;
	u64		tx_collide_3times;
	u64		tx_collide_4times;
	u64		tx_collide_5times;
	u64		tx_collide_6times;
	u64		tx_collide_7times;
	u64		tx_collide_8times;
	u64		tx_collide_9times;
	u64		tx_collide_10times;
	u64		tx_collide_11times;
	u64		tx_collide_12times;
	u64		tx_collide_13times;
	u64		tx_collide_14times;
	u64		tx_collide_15times;
	u64		tx_ucast_packets;
	u64		tx_mcast_packets;
	u64		tx_bcast_packets;
	u64		tx_carrier_sense_errors;
	u64		tx_discards;
	u64		tx_errors;

	/* Statistics maintained by Receive List Placement. */
	u64		dma_writeq_full;
	u64		dma_write_prioq_full;
	u64		rxbds_empty;
	u64		rx_discards;
	u64		rx_errors;
	u64		rx_threshold_hit;

	/* Statistics maintained by Send Data Initiator. */
	u64		dma_readq_full;
	u64		dma_read_prioq_full;
	u64		tx_comp_queue_full;

	/* Statistics maintained by Host Coalescing. */
	u64		ring_set_send_prod_index;
	u64		ring_status_update;
	u64		nic_irqs;
	u64		nic_avoided_irqs;
	u64		nic_tx_threshold_hit;

	u64		mbuf_lwm_thresh_hit;
};

struct tg3_rx_prodring_set {
	u32				rx_std_prod_idx;
	u32				rx_std_cons_idx;
	u32				rx_jmb_prod_idx;
	u32				rx_jmb_cons_idx;
	struct tg3_rx_buffer_desc	*rx_std;
	struct tg3_ext_rx_buffer_desc	*rx_jmb;
	struct ring_info		*rx_std_buffers;
	struct ring_info		*rx_jmb_buffers;
	dma_addr_t			rx_std_mapping;
	dma_addr_t			rx_jmb_mapping;
};

#define TG3_RSS_MAX_NUM_QS		4
#define TG3_IRQ_MAX_VECS_RSS		(TG3_RSS_MAX_NUM_QS + 1)
#define TG3_IRQ_MAX_VECS		TG3_IRQ_MAX_VECS_RSS

struct tg3_napi {
	struct napi_struct		napi	____cacheline_aligned;
	struct tg3			*tp;
	struct tg3_hw_status		*hw_status;

	u32				chk_msi_cnt;
	u32				last_tag;
	u32				last_irq_tag;
	u32				int_mbox;
	u32				coal_now;

	u32				consmbox ____cacheline_aligned;
	u32				rx_rcb_ptr;
	u32				last_rx_cons;
	u16				*rx_rcb_prod_idx;
	struct tg3_rx_prodring_set	prodring;
	struct tg3_rx_buffer_desc	*rx_rcb;

	u32				tx_prod	____cacheline_aligned;
	u32				tx_cons;
	u32				tx_pending;
	u32				last_tx_cons;
	u32				prodmbox;
	struct tg3_tx_buffer_desc	*tx_ring;
	struct tg3_tx_ring_info		*tx_buffers;

	dma_addr_t			status_mapping;
	dma_addr_t			rx_rcb_mapping;
	dma_addr_t			tx_desc_mapping;

	char				irq_lbl[IFNAMSIZ];
	unsigned int			irq_vec;
};

enum TG3_FLAGS {
	TG3_FLAG_TAGGED_STATUS = 0,
	TG3_FLAG_TXD_MBOX_HWBUG,
	TG3_FLAG_USE_LINKCHG_REG,
	TG3_FLAG_ERROR_PROCESSED,
	TG3_FLAG_ENABLE_ASF,
	TG3_FLAG_ASPM_WORKAROUND,
	TG3_FLAG_POLL_SERDES,
	TG3_FLAG_POLL_CPMU_LINK,
	TG3_FLAG_MBOX_WRITE_REORDER,
	TG3_FLAG_PCIX_TARGET_HWBUG,
	TG3_FLAG_WOL_SPEED_100MB,
	TG3_FLAG_WOL_ENABLE,
	TG3_FLAG_EEPROM_WRITE_PROT,
	TG3_FLAG_NVRAM,
	TG3_FLAG_NVRAM_BUFFERED,
	TG3_FLAG_SUPPORT_MSI,
	TG3_FLAG_SUPPORT_MSIX,
	TG3_FLAG_USING_MSI,
	TG3_FLAG_USING_MSIX,
	TG3_FLAG_PCIX_MODE,
	TG3_FLAG_PCI_HIGH_SPEED,
	TG3_FLAG_PCI_32BIT,
	TG3_FLAG_SRAM_USE_CONFIG,
	TG3_FLAG_TX_RECOVERY_PENDING,
	TG3_FLAG_WOL_CAP,
	TG3_FLAG_JUMBO_RING_ENABLE,
	TG3_FLAG_PAUSE_AUTONEG,
	TG3_FLAG_CPMU_PRESENT,
	TG3_FLAG_40BIT_DMA_BUG,
	TG3_FLAG_BROKEN_CHECKSUMS,
	TG3_FLAG_JUMBO_CAPABLE,
	TG3_FLAG_CHIP_RESETTING,
	TG3_FLAG_INIT_COMPLETE,
	TG3_FLAG_MAX_RXPEND_64,
	TG3_FLAG_PCI_EXPRESS, /* BCM5785 + pci_is_pcie() */
	TG3_FLAG_ASF_NEW_HANDSHAKE,
	TG3_FLAG_HW_AUTONEG,
	TG3_FLAG_IS_NIC,
	TG3_FLAG_FLASH,
	TG3_FLAG_FW_TSO,
	TG3_FLAG_HW_TSO_1,
	TG3_FLAG_HW_TSO_2,
	TG3_FLAG_HW_TSO_3,
	TG3_FLAG_TSO_CAPABLE,
	TG3_FLAG_TSO_BUG,
	TG3_FLAG_ICH_WORKAROUND,
	TG3_FLAG_1SHOT_MSI,
	TG3_FLAG_NO_FWARE_REPORTED,
	TG3_FLAG_NO_NVRAM_ADDR_TRANS,
	TG3_FLAG_ENABLE_APE,
	TG3_FLAG_PROTECTED_NVRAM,
	TG3_FLAG_5701_DMA_BUG,
	TG3_FLAG_USE_PHYLIB,
	TG3_FLAG_MDIOBUS_INITED,
	TG3_FLAG_LRG_PROD_RING_CAP,
	TG3_FLAG_RGMII_INBAND_DISABLE,
	TG3_FLAG_RGMII_EXT_IBND_RX_EN,
	TG3_FLAG_RGMII_EXT_IBND_TX_EN,
	TG3_FLAG_CLKREQ_BUG,
	TG3_FLAG_NO_NVRAM,
	TG3_FLAG_ENABLE_RSS,
	TG3_FLAG_ENABLE_TSS,
	TG3_FLAG_SHORT_DMA_BUG,
	TG3_FLAG_USE_JUMBO_BDFLAG,
	TG3_FLAG_L1PLLPD_EN,
	TG3_FLAG_APE_HAS_NCSI,
	TG3_FLAG_TX_TSTAMP_EN,
	TG3_FLAG_4K_FIFO_LIMIT,
	TG3_FLAG_5719_5720_RDMA_BUG,
	TG3_FLAG_RESET_TASK_PENDING,
	TG3_FLAG_PTP_CAPABLE,
	TG3_FLAG_5705_PLUS,
	TG3_FLAG_IS_5788,
	TG3_FLAG_5750_PLUS,
	TG3_FLAG_5780_CLASS,
	TG3_FLAG_5755_PLUS,
	TG3_FLAG_57765_PLUS,
	TG3_FLAG_57765_CLASS,
	TG3_FLAG_5717_PLUS,
	TG3_FLAG_IS_SSB_CORE,
	TG3_FLAG_FLUSH_POSTED_WRITES,
	TG3_FLAG_ROBOSWITCH,
	TG3_FLAG_ONE_DMA_AT_ONCE,
	TG3_FLAG_RGMII_MODE,

	/* Add new flags before this comment and TG3_FLAG_NUMBER_OF_FLAGS */
	TG3_FLAG_NUMBER_OF_FLAGS,	/* Last entry in enum TG3_FLAGS */
};

struct tg3_firmware_hdr {
	__be32 version; /* unused for fragments */
	__be32 base_addr;
	__be32 len;
};
#define TG3_FW_HDR_LEN         (sizeof(struct tg3_firmware_hdr))

struct tg3 {
	/* begin "general, frequently-used members" cacheline section */

	/* If the IRQ handler (which runs lockless) needs to be
	 * quiesced, the following bitmask state is used.  The
	 * SYNC flag is set by non-IRQ context code to initiate
	 * the quiescence.
	 *
	 * When the IRQ handler notices that SYNC is set, it
	 * disables interrupts and returns.
	 *
	 * When all outstanding IRQ handlers have returned after
	 * the SYNC flag has been set, the setter can be assured
	 * that interrupts will no longer get run.
	 *
	 * In this way all SMP driver locks are never acquired
	 * in hw IRQ context, only sw IRQ context or lower.
	 */
	unsigned int			irq_sync;

	/* SMP locking strategy:
	 *
	 * lock: Held during reset, PHY access, timer, and when
	 *       updating tg3_flags.
	 *
	 * netif_tx_lock: Held during tg3_start_xmit. tg3_tx holds
	 *                netif_tx_lock when it needs to call
	 *                netif_wake_queue.
	 *
	 * Both of these locks are to be held with BH safety.
	 *
	 * Because the IRQ handler, tg3_poll, and tg3_start_xmit
	 * are running lockless, it is necessary to completely
	 * quiesce the chip with tg3_netif_stop and tg3_full_lock
	 * before reconfiguring the device.
	 *
	 * indirect_lock: Held when accessing registers indirectly
	 *                with IRQ disabling.
	 */
	spinlock_t			lock;
	spinlock_t			indirect_lock;

	u32				(*read32) (struct tg3 *, u32);
	void				(*write32) (struct tg3 *, u32, u32);
	u32				(*read32_mbox) (struct tg3 *, u32);
	void				(*write32_mbox) (struct tg3 *, u32,
							 u32);
	void __iomem			*regs;
	void __iomem			*aperegs;
	struct net_device		*dev;
	struct pci_dev			*pdev;

	u32				coal_now;
	u32				msg_enable;

	struct ptp_clock_info		ptp_info;
	struct ptp_clock		*ptp_clock;
	s64				ptp_adjust;

	/* begin "tx thread" cacheline section */
	void				(*write32_tx_mbox) (struct tg3 *, u32,
							    u32);
	u32				dma_limit;
	u32				txq_req;
	u32				txq_cnt;
	u32				txq_max;

	/* begin "rx thread" cacheline section */
	struct tg3_napi			napi[TG3_IRQ_MAX_VECS];
	void				(*write32_rx_mbox) (struct tg3 *, u32,
							    u32);
	u32				rx_copy_thresh;
	u32				rx_std_ring_mask;
	u32				rx_jmb_ring_mask;
	u32				rx_ret_ring_mask;
	u32				rx_pending;
	u32				rx_jumbo_pending;
	u32				rx_std_max_post;
	u32				rx_offset;
	u32				rx_pkt_map_sz;
	u32				rxq_req;
	u32				rxq_cnt;
	u32				rxq_max;
	bool				rx_refill;


	/* begin "everything else" cacheline(s) section */
	unsigned long			rx_dropped;
	unsigned long			tx_dropped;
	struct rtnl_link_stats64	net_stats_prev;
	struct tg3_ethtool_stats	estats_prev;

	DECLARE_BITMAP(tg3_flags, TG3_FLAG_NUMBER_OF_FLAGS);

	union {
	unsigned long			phy_crc_errors;
	unsigned long			last_event_jiffies;
	};

	struct timer_list		timer;
	u16				timer_counter;
	u16				timer_multiplier;
	u32				timer_offset;
	u16				asf_counter;
	u16				asf_multiplier;

	/* 1 second counter for transient serdes link events */
	u32				serdes_counter;
#define SERDES_AN_TIMEOUT_5704S		2
#define SERDES_PARALLEL_DET_TIMEOUT	1
#define SERDES_AN_TIMEOUT_5714S		1

	struct tg3_link_config		link_config;
	struct tg3_bufmgr_config	bufmgr_config;

	/* cache h/w values, often passed straight to h/w */
	u32				rx_mode;
	u32				tx_mode;
	u32				mac_mode;
	u32				mi_mode;
	u32				misc_host_ctrl;
	u32				grc_mode;
	u32				grc_local_ctrl;
	u32				dma_rwctrl;
	u32				coalesce_mode;
	u32				pwrmgmt_thresh;
	u32				rxptpctl;

	/* PCI block */
	u32				pci_chip_rev_id;
	u16				pci_cmd;
	u8				pci_cacheline_sz;
	u8				pci_lat_timer;

	int				pci_fn;
	int				msi_cap;
	int				pcix_cap;
	int				pcie_readrq;

	struct mii_bus			*mdio_bus;
	int				old_link;

	u8				phy_addr;
	u8				phy_ape_lock;

	/* PHY info */
	u32				phy_id;
#define TG3_PHY_ID_MASK			0xfffffff0
#define TG3_PHY_ID_BCM5400		0x60008040
#define TG3_PHY_ID_BCM5401		0x60008050
#define TG3_PHY_ID_BCM5411		0x60008070
#define TG3_PHY_ID_BCM5701		0x60008110
#define TG3_PHY_ID_BCM5703		0x60008160
#define TG3_PHY_ID_BCM5704		0x60008190
#define TG3_PHY_ID_BCM5705		0x600081a0
#define TG3_PHY_ID_BCM5750		0x60008180
#define TG3_PHY_ID_BCM5752		0x60008100
#define TG3_PHY_ID_BCM5714		0x60008340
#define TG3_PHY_ID_BCM5780		0x60008350
#define TG3_PHY_ID_BCM5755		0xbc050cc0
#define TG3_PHY_ID_BCM5787		0xbc050ce0
#define TG3_PHY_ID_BCM5756		0xbc050ed0
#define TG3_PHY_ID_BCM5784		0xbc050fa0
#define TG3_PHY_ID_BCM5761		0xbc050fd0
#define TG3_PHY_ID_BCM5718C		0x5c0d8a00
#define TG3_PHY_ID_BCM5718S		0xbc050ff0
#define TG3_PHY_ID_BCM57765		0x5c0d8a40
#define TG3_PHY_ID_BCM5719C		0x5c0d8a20
#define TG3_PHY_ID_BCM5720C		0x5c0d8b60
#define TG3_PHY_ID_BCM5762		0x85803780
#define TG3_PHY_ID_BCM5906		0xdc00ac40
#define TG3_PHY_ID_BCM8002		0x60010140
#define TG3_PHY_ID_INVALID		0xffffffff

#define PHY_ID_RTL8211C			0x001cc910
#define PHY_ID_RTL8201E			0x00008200

#define TG3_PHY_ID_REV_MASK		0x0000000f
#define TG3_PHY_REV_BCM5401_B0		0x1

	/* This macro assumes the passed PHY ID is
	 * already masked with TG3_PHY_ID_MASK.
	 */
#define TG3_KNOWN_PHY_ID(X)		\
	((X) == TG3_PHY_ID_BCM5400 || (X) == TG3_PHY_ID_BCM5401 || \
	 (X) == TG3_PHY_ID_BCM5411 || (X) == TG3_PHY_ID_BCM5701 || \
	 (X) == TG3_PHY_ID_BCM5703 || (X) == TG3_PHY_ID_BCM5704 || \
	 (X) == TG3_PHY_ID_BCM5705 || (X) == TG3_PHY_ID_BCM5750 || \
	 (X) == TG3_PHY_ID_BCM5752 || (X) == TG3_PHY_ID_BCM5714 || \
	 (X) == TG3_PHY_ID_BCM5780 || (X) == TG3_PHY_ID_BCM5787 || \
	 (X) == TG3_PHY_ID_BCM5755 || (X) == TG3_PHY_ID_BCM5756 || \
	 (X) == TG3_PHY_ID_BCM5906 || (X) == TG3_PHY_ID_BCM5761 || \
	 (X) == TG3_PHY_ID_BCM5718C || (X) == TG3_PHY_ID_BCM5718S || \
	 (X) == TG3_PHY_ID_BCM57765 || (X) == TG3_PHY_ID_BCM5719C || \
	 (X) == TG3_PHY_ID_BCM5720C || (X) == TG3_PHY_ID_BCM5762 || \
	 (X) == TG3_PHY_ID_BCM8002)

	u32				phy_flags;
#define TG3_PHYFLG_IS_LOW_POWER		0x00000001
#define TG3_PHYFLG_IS_CONNECTED		0x00000002
#define TG3_PHYFLG_USE_MI_INTERRUPT	0x00000004
#define TG3_PHYFLG_USER_CONFIGURED	0x00000008
#define TG3_PHYFLG_PHY_SERDES		0x00000010
#define TG3_PHYFLG_MII_SERDES		0x00000020
#define TG3_PHYFLG_ANY_SERDES		(TG3_PHYFLG_PHY_SERDES |	\
					TG3_PHYFLG_MII_SERDES)
#define TG3_PHYFLG_IS_FET		0x00000040
#define TG3_PHYFLG_10_100_ONLY		0x00000080
#define TG3_PHYFLG_ENABLE_APD		0x00000100
#define TG3_PHYFLG_CAPACITIVE_COUPLING	0x00000200
#define TG3_PHYFLG_NO_ETH_WIRE_SPEED	0x00000400
#define TG3_PHYFLG_JITTER_BUG		0x00000800
#define TG3_PHYFLG_ADJUST_TRIM		0x00001000
#define TG3_PHYFLG_ADC_BUG		0x00002000
#define TG3_PHYFLG_5704_A0_BUG		0x00004000
#define TG3_PHYFLG_BER_BUG		0x00008000
#define TG3_PHYFLG_SERDES_PREEMPHASIS	0x00010000
#define TG3_PHYFLG_PARALLEL_DETECT	0x00020000
#define TG3_PHYFLG_EEE_CAP		0x00040000
#define TG3_PHYFLG_1G_ON_VAUX_OK	0x00080000
#define TG3_PHYFLG_KEEP_LINK_ON_PWRDN	0x00100000
#define TG3_PHYFLG_MDIX_STATE		0x00200000
#define TG3_PHYFLG_DISABLE_1G_HD_ADV	0x00400000

	u32				led_ctrl;
	u32				phy_otp;
	u32				setlpicnt;
	u8				rss_ind_tbl[TG3_RSS_INDIR_TBL_SIZE];

#define TG3_BPN_SIZE			24
	char				board_part_number[TG3_BPN_SIZE];
#define TG3_VER_SIZE			ETHTOOL_FWVERS_LEN
	char				fw_ver[TG3_VER_SIZE];
	u32				nic_sram_data_cfg;
	u32				pci_clock_ctrl;
	struct pci_dev			*pdev_peer;

	struct tg3_hw_stats		*hw_stats;
	dma_addr_t			stats_mapping;
	struct work_struct		reset_task;

	int				nvram_lock_cnt;
	u32				nvram_size;
#define TG3_NVRAM_SIZE_2KB		0x00000800
#define TG3_NVRAM_SIZE_64KB		0x00010000
#define TG3_NVRAM_SIZE_128KB		0x00020000
#define TG3_NVRAM_SIZE_256KB		0x00040000
#define TG3_NVRAM_SIZE_512KB		0x00080000
#define TG3_NVRAM_SIZE_1MB		0x00100000
#define TG3_NVRAM_SIZE_2MB		0x00200000

	u32				nvram_pagesize;
	u32				nvram_jedecnum;

#define JEDEC_ATMEL			0x1f
#define JEDEC_ST			0x20
#define JEDEC_SAIFUN			0x4f
#define JEDEC_SST			0xbf
#define JEDEC_MACRONIX                 0xc2

#define ATMEL_AT24C02_CHIP_SIZE		TG3_NVRAM_SIZE_2KB
#define ATMEL_AT24C02_PAGE_SIZE		(8)

#define ATMEL_AT24C64_CHIP_SIZE		TG3_NVRAM_SIZE_64KB
#define ATMEL_AT24C64_PAGE_SIZE		(32)

#define ATMEL_AT24C512_CHIP_SIZE	TG3_NVRAM_SIZE_512KB
#define ATMEL_AT24C512_PAGE_SIZE	(128)

#define ATMEL_AT45DB0X1B_PAGE_POS	9
#define ATMEL_AT45DB0X1B_PAGE_SIZE	264

#define ATMEL_AT25F512_PAGE_SIZE	256

#define ST_M45PEX0_PAGE_SIZE		256

#define SAIFUN_SA25F0XX_PAGE_SIZE	256

#define SST_25VF0X0_PAGE_SIZE		4098

	unsigned int			irq_max;
	unsigned int			irq_cnt;

	struct ethtool_coalesce		coal;
	struct ethtool_eee		eee;

	/* firmware info */
	const char			*fw_needed;
	const struct firmware		*fw;
	u32				fw_len; /* includes BSS */

	struct device			*hwmon_dev;
	bool				link_up;
	bool				pcierr_recovery;

	u32                             ape_hb;
	unsigned long                   ape_hb_interval;
	unsigned long                   ape_hb_jiffies;
};

/* Accessor macros for chip and asic attributes
 *
 * nb: Using static inlines equivalent to the accessor macros generates
 *     larger object code with gcc 4.7.
 *     Using statement expression macros to check tp with
 *     typecheck(struct tg3 *, tp) also creates larger objects.
 */
#define tg3_chip_rev_id(tp)					\
	((tp)->pci_chip_rev_id)
#define tg3_asic_rev(tp)					\
	((tp)->pci_chip_rev_id >> 12)
#define tg3_chip_rev(tp)					\
	((tp)->pci_chip_rev_id >> 8)

#endif /* !(_T3_H) */
