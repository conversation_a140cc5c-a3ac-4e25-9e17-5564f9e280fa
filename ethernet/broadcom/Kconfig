# SPDX-License-Identifier: GPL-2.0-only
#
# Broadcom device configuration
#

config NET_VENDOR_BROADCOM
	bool "Broadcom devices"
	default y
	depends on (SSB_POSSIBLE && HAS_DMA) || PCI || BCM63XX || \
		   SIBYTE_SB1xxx_SOC
	---help---
	  If you have a network (Ethernet) chipset belonging to this class,
	  say Y.

	  Note that the answer to this question does not directly affect
	  the kernel: saying N will just cause the configurator to skip all
	  the questions regarding Broadcom chipsets. If you say Y, you will
	  be asked for your specific chipset/driver in the following questions.

if NET_VENDOR_BROADCOM

config B44
	tristate "Broadcom 440x/47xx ethernet support"
	depends on SSB_POSSIBLE && HAS_DMA
	select SSB
	select MII
	select PHYLIB
	---help---
	  If you have a network (Ethernet) controller of this type, say Y
	  or M here.

	  To compile this driver as a module, choose M here. The module
	  will be called b44.

# Auto-select SSB PCI-HOST support, if possible
config B44_PCI_AUTOSELECT
	bool
	depends on B44 && SSB_PCIHOST_POSSIBLE
	select SSB_PCIHOST
	default y

# Auto-select SSB PCICORE driver, if possible
config B44_PCICORE_AUTOSELECT
	bool
	depends on B44 && SSB_DRIVER_PCICORE_POSSIBLE
	select SSB_DRIVER_PCICORE
	default y

config B44_PCI
	bool
	depends on B44_PCI_AUTOSELECT && B44_PCICORE_AUTOSELECT
	default y

config BCM63XX_ENET
	tristate "Broadcom 63xx internal mac support"
	depends on BCM63XX
	select MII
	select PHYLIB
	help
	  This driver supports the ethernet MACs in the Broadcom 63xx
	  MIPS chipset family (BCM63XX).

config BCMGENET
	tristate "Broadcom GENET internal MAC support"
	depends on HAS_IOMEM
	select MII
	select PHYLIB
	select FIXED_PHY
	select BCM7XXX_PHY
	select MDIO_BCM_UNIMAC
	select DIMLIB
	help
	  This driver supports the built-in Ethernet MACs found in the
	  Broadcom BCM7xxx Set Top Box family chipset.

config BNX2
	tristate "QLogic bnx2 support"
	depends on PCI
	select CRC32
	select FW_LOADER
	---help---
	  This driver supports QLogic bnx2 gigabit Ethernet cards.

	  To compile this driver as a module, choose M here: the module
	  will be called bnx2.  This is recommended.

config CNIC
	tristate "QLogic CNIC support"
	depends on PCI && (IPV6 || IPV6=n)
	select BNX2
	select UIO
	---help---
	  This driver supports offload features of QLogic bnx2 gigabit
	  Ethernet cards.

	  To compile this driver as a module, choose M here: the module
	  will be called cnic.  This is recommended.

config SB1250_MAC
	tristate "SB1250 Gigabit Ethernet support"
	depends on SIBYTE_SB1xxx_SOC
	select PHYLIB
	---help---
	  This driver supports Gigabit Ethernet interfaces based on the
	  Broadcom SiByte family of System-On-a-Chip parts.  They include
	  the BCM1120, BCM1125, BCM1125H, BCM1250, BCM1255, BCM1280, BCM1455
	  and BCM1480 chips.

	  To compile this driver as a module, choose M here: the module
	  will be called sb1250-mac.

config TIGON3
	tristate "Broadcom Tigon3 support"
	depends on PCI
	select PHYLIB
	imply PTP_1588_CLOCK
	---help---
	  This driver supports Broadcom Tigon3 based gigabit Ethernet cards.

	  To compile this driver as a module, choose M here: the module
	  will be called tg3.  This is recommended.

config TIGON3_HWMON
	bool "Broadcom Tigon3 HWMON support"
	default y
	depends on TIGON3 && HWMON && !(TIGON3=y && HWMON=m)
	---help---
	  Say Y if you want to expose the thermal sensor on Tigon3 devices.

config BNX2X
	tristate "Broadcom NetXtremeII 10Gb support"
	depends on PCI
	imply PTP_1588_CLOCK
	select FW_LOADER
	select ZLIB_INFLATE
	select LIBCRC32C
	select MDIO
	---help---
	  This driver supports Broadcom NetXtremeII 10 gigabit Ethernet cards.
	  To compile this driver as a module, choose M here: the module
	  will be called bnx2x.  This is recommended.

config BNX2X_SRIOV
	bool "Broadcom 578xx and 57712 SR-IOV support"
	depends on BNX2X && PCI_IOV
	default y
	---help---
	  This configuration parameter enables Single Root Input Output
	  Virtualization support in the 578xx and 57712 products. This
	  allows for virtual function acceleration in virtual environments.

config BGMAC
	tristate
	help
	  This enables the integrated ethernet controller support for many
	  Broadcom (mostly iProc) SoCs. An appropriate bus interface driver
	  needs to be enabled to select this.

config BGMAC_BCMA
	tristate "Broadcom iProc GBit BCMA support"
	depends on BCMA && BCMA_HOST_SOC
	depends on BCM47XX || ARCH_BCM_5301X || COMPILE_TEST
	select BGMAC
	select PHYLIB
	select FIXED_PHY
	---help---
	  This driver supports GBit MAC and BCM4706 GBit MAC cores on BCMA bus.
	  They can be found on BCM47xx SoCs and provide gigabit ethernet.
	  In case of using this driver on BCM4706 it's also requires to enable
	  BCMA_DRIVER_GMAC_CMN to make it work.

config BGMAC_PLATFORM
	tristate "Broadcom iProc GBit platform support"
	depends on ARCH_BCM_IPROC || COMPILE_TEST
	depends on OF
	select BGMAC
	select PHYLIB
	select FIXED_PHY
	default ARCH_BCM_IPROC
	---help---
	  Say Y here if you want to use the Broadcom iProc Gigabit Ethernet
	  controller through the generic platform interface

config SYSTEMPORT
	tristate "Broadcom SYSTEMPORT internal MAC support"
	depends on HAS_IOMEM
	depends on NET_DSA || !NET_DSA
	select MII
	select PHYLIB
	select FIXED_PHY
	select DIMLIB
	help
	  This driver supports the built-in Ethernet MACs found in the
	  Broadcom BCM7xxx Set Top Box family chipset using an internal
	  Ethernet switch.

config BNXT
	tristate "Broadcom NetXtreme-C/E support"
	depends on PCI
	select FW_LOADER
	select LIBCRC32C
	select NET_DEVLINK
	select PAGE_POOL
	select DIMLIB
	---help---
	  This driver supports Broadcom NetXtreme-C/E 10/25/40/50 gigabit
	  Ethernet cards.  To compile this driver as a module, choose M here:
	  the module will be called bnxt_en.  This is recommended.

config BNXT_SRIOV
	bool "Broadcom NetXtreme-C/E SR-IOV support"
	depends on BNXT && PCI_IOV
	default y
	---help---
	  This configuration parameter enables Single Root Input Output
	  Virtualization support in the NetXtreme-C/E products. This
	  allows for virtual function acceleration in virtual environments.

config BNXT_FLOWER_OFFLOAD
	bool "TC Flower offload support for NetXtreme-C/E"
	depends on BNXT
	default y
	---help---
	  This configuration parameter enables TC Flower packet classifier
	  offload for eswitch.  This option enables SR-IOV switchdev eswitch
	  offload.

config BNXT_DCB
	bool "Data Center Bridging (DCB) Support"
	default n
	depends on BNXT && DCB
	---help---
	  Say Y here if you want to use Data Center Bridging (DCB) in the
	  driver.

	  If unsure, say N.

config BNXT_HWMON
	bool "Broadcom NetXtreme-C/E HWMON support"
	default y
	depends on BNXT && HWMON && !(BNXT=y && HWMON=m)
	---help---
	  Say Y if you want to expose the thermal sensor data on NetXtreme-C/E
	  devices, via the hwmon sysfs interface.

endif # NET_VENDOR_BROADCOM
