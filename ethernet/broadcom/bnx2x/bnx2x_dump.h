/* bnx2x_dump.h: QLogic Everest network driver.
 *
 * Copyright (c) 2012-2013 Broadcom Corporation
 * Copyright (c) 2014 QLogic Corporation
 * All rights reserved
 *
 * Unless you and <PERSON>Logic execute a separate written software license
 * agreement governing use of this software, this software is licensed to you
 * under the terms of the GNU General Public License version 2, available
 * at http://www.gnu.org/licenses/old-licenses/gpl-2.0.html (the "GPL").
 *
 * Notwithstanding the above, under no circumstances may you combine this
 * software in any way with any other QLogic software provided under a
 * license other than the GPL, without QLogic's express prior written
 * consent.
 */

#ifndef BNX2X_DUMP_H
#define BNX2X_DUMP_H

/* WaitP Definitions */
#define DRV_DUMP_XSTORM_WAITP_ADDRESS    0x2b8a80
#define DRV_DUMP_TSTORM_WAITP_ADDRESS    0x1b8a80
#define DRV_DUMP_USTORM_WAITP_ADDRESS    0x338a80
#define DRV_DUMP_CSTORM_WAITP_ADDRESS    0x238a80

/* Possible Chips */
#define DUMP_CHIP_E1 1
#define DUMP_CHIP_E1H 2
#define DUMP_CHIP_E2 4
#define DUMP_CHIP_E3A0 8
#define DUMP_CHIP_E3B0 16
#define DUMP_PATH_0 512
#define DUMP_PATH_1 1024
#define NUM_PRESETS 13
#define NUM_CHIPS 5

struct	dump_header {
	u32 header_size; /* Size in DWORDs excluding this field */
	u32 version;
	u32 preset;
	u32 dump_meta_data; /* OR of CHIP and PATH. */
};

#define  BNX2X_DUMP_VERSION 0x61111111
struct reg_addr {
	u32 addr;
	u32 size;
	u32 chips;
	u32 presets;
};

struct wreg_addr {
	u32 addr;
	u32 size;
	u32 read_regs_count;
	const u32 *read_regs;
	u32 chips;
	u32 presets;
};

#define PAGE_MODE_VALUES_E2 2
#define PAGE_READ_REGS_E2 1
#define PAGE_WRITE_REGS_E2 1
static const u32 page_vals_e2[] = {0, 128};
static const u32 page_write_regs_e2[] = {328476};
static const struct reg_addr page_read_regs_e2[] = {
	{0x58000, 4608, DUMP_CHIP_E2, 0x30}
};

#define PAGE_MODE_VALUES_E3 2
#define PAGE_READ_REGS_E3 1
#define PAGE_WRITE_REGS_E3 1
static const u32 page_vals_e3[] = {0, 128};
static const u32 page_write_regs_e3[] = {328476};
static const struct reg_addr page_read_regs_e3[] = {
	{0x58000, 4608, DUMP_CHIP_E3A0 | DUMP_CHIP_E3B0, 0x30}
};

static const struct reg_addr reg_addrs[] = {
	{ 0x2000, 1, 0x1f, 0xfff},
	{ 0x2004, 1, 0x1f, 0x1fff},
	{ 0x2008, 25, 0x1f, 0xfff},
	{ 0x206c, 1, 0x1f, 0x1fff},
	{ 0x2070, 313, 0x1f, 0xfff},
	{ 0x2800, 103, 0x1f, 0xfff},
	{ 0x3000, 287, 0x1f, 0xfff},
	{ 0x3800, 331, 0x1f, 0xfff},
	{ 0x8800, 6, 0x1f, 0x924},
	{ 0x8818, 1, 0x1e, 0x924},
	{ 0x9000, 4, 0x1c, 0x924},
	{ 0x9010, 7, 0x1c, 0xfff},
	{ 0x902c, 1, 0x1c, 0x924},
	{ 0x9030, 1, 0x1c, 0xfff},
	{ 0x9034, 13, 0x1c, 0x924},
	{ 0x9068, 16, 0x1c, 0xfff},
	{ 0x90a8, 98, 0x1c, 0x924},
	{ 0x9230, 2, 0x1c, 0xfff},
	{ 0x9238, 3, 0x1c, 0x924},
	{ 0x9244, 1, 0x1c, 0xfff},
	{ 0x9248, 1, 0x1c, 0x924},
	{ 0x924c, 1, 0x4, 0x924},
	{ 0x9250, 16, 0x1c, 0x924},
	{ 0x92a8, 2, 0x1c, 0x1fff},
	{ 0x92b4, 1, 0x1c, 0x1fff},
	{ 0x9400, 33, 0x1c, 0x924},
	{ 0x9484, 5, 0x18, 0x924},
	{ 0xa000, 27, 0x1f, 0x924},
	{ 0xa06c, 1, 0x3, 0x924},
	{ 0xa070, 2, 0x1f, 0x924},
	{ 0xa078, 1, 0x1f, 0x1fff},
	{ 0xa07c, 31, 0x1f, 0x924},
	{ 0xa0f8, 1, 0x1f, 0x1fff},
	{ 0xa0fc, 3, 0x1f, 0x924},
	{ 0xa108, 1, 0x1f, 0x1fff},
	{ 0xa10c, 3, 0x1f, 0x924},
	{ 0xa118, 1, 0x1f, 0x1fff},
	{ 0xa11c, 28, 0x1f, 0x924},
	{ 0xa18c, 4, 0x3, 0x924},
	{ 0xa19c, 3, 0x1f, 0x924},
	{ 0xa1a8, 1, 0x1f, 0x1fff},
	{ 0xa1ac, 3, 0x1f, 0x924},
	{ 0xa1b8, 1, 0x1f, 0x1fff},
	{ 0xa1bc, 54, 0x1f, 0x924},
	{ 0xa294, 2, 0x3, 0x924},
	{ 0xa29c, 2, 0x1f, 0x924},
	{ 0xa2a4, 2, 0x7, 0x924},
	{ 0xa2ac, 2, 0x1f, 0x924},
	{ 0xa2b4, 1, 0x1f, 0x1fff},
	{ 0xa2b8, 49, 0x1f, 0x924},
	{ 0xa38c, 2, 0x1f, 0x1fff},
	{ 0xa398, 1, 0x1f, 0x1fff},
	{ 0xa39c, 7, 0x1e, 0x924},
	{ 0xa3b8, 2, 0x18, 0x924},
	{ 0xa3c0, 1, 0x1e, 0x924},
	{ 0xa3c4, 1, 0x1e, 0xfff},
	{ 0xa3c8, 1, 0x1e, 0x924},
	{ 0xa3d0, 1, 0x1e, 0x924},
	{ 0xa3d8, 1, 0x1e, 0x924},
	{ 0xa3e0, 1, 0x1e, 0x924},
	{ 0xa3e8, 1, 0x1e, 0x924},
	{ 0xa3f0, 1, 0x1e, 0x924},
	{ 0xa3f8, 1, 0x1e, 0x924},
	{ 0xa400, 1, 0x1f, 0x924},
	{ 0xa404, 1, 0x1f, 0xfff},
	{ 0xa408, 2, 0x1f, 0x1fff},
	{ 0xa410, 7, 0x1f, 0x924},
	{ 0xa42c, 12, 0x1f, 0xfff},
	{ 0xa45c, 1, 0x1f, 0x924},
	{ 0xa460, 1, 0x1f, 0x1924},
	{ 0xa464, 15, 0x1f, 0x924},
	{ 0xa4a0, 1, 0x7, 0x924},
	{ 0xa4a4, 2, 0x1f, 0x924},
	{ 0xa4ac, 2, 0x3, 0x924},
	{ 0xa4b4, 1, 0x7, 0x924},
	{ 0xa4b8, 2, 0x3, 0x924},
	{ 0xa4c0, 3, 0x1f, 0x924},
	{ 0xa4cc, 5, 0x3, 0x924},
	{ 0xa4e0, 3, 0x1f, 0x924},
	{ 0xa4fc, 2, 0x1f, 0x924},
	{ 0xa504, 1, 0x3, 0x924},
	{ 0xa508, 3, 0x1f, 0x924},
	{ 0xa518, 1, 0x1f, 0x924},
	{ 0xa520, 1, 0x1f, 0x924},
	{ 0xa528, 1, 0x1f, 0x924},
	{ 0xa530, 1, 0x1f, 0x924},
	{ 0xa538, 1, 0x1f, 0x924},
	{ 0xa540, 1, 0x1f, 0x924},
	{ 0xa548, 1, 0x3, 0x924},
	{ 0xa550, 1, 0x3, 0x924},
	{ 0xa558, 1, 0x3, 0x924},
	{ 0xa560, 1, 0x3, 0x924},
	{ 0xa568, 1, 0x3, 0x924},
	{ 0xa570, 1, 0x1f, 0x924},
	{ 0xa580, 1, 0x1f, 0x1fff},
	{ 0xa590, 1, 0x1f, 0x1fff},
	{ 0xa5a0, 1, 0x7, 0x924},
	{ 0xa5c0, 1, 0x1f, 0x924},
	{ 0xa5e0, 1, 0x1e, 0x924},
	{ 0xa5e8, 1, 0x1e, 0x924},
	{ 0xa5f0, 1, 0x1e, 0x924},
	{ 0xa5f8, 1, 0x6, 0x924},
	{ 0xa5fc, 1, 0x1e, 0x924},
	{ 0xa600, 5, 0x1e, 0xfff},
	{ 0xa614, 1, 0x1e, 0x924},
	{ 0xa618, 1, 0x1e, 0xfff},
	{ 0xa61c, 1, 0x1e, 0x924},
	{ 0xa620, 6, 0x1c, 0x924},
	{ 0xa638, 20, 0x4, 0x924},
	{ 0xa688, 35, 0x1c, 0x924},
	{ 0xa714, 1, 0x1c, 0xfff},
	{ 0xa718, 2, 0x1c, 0x924},
	{ 0xa720, 1, 0x1c, 0xfff},
	{ 0xa724, 3, 0x1c, 0x924},
	{ 0xa730, 1, 0x4, 0x924},
	{ 0xa734, 2, 0x1c, 0x924},
	{ 0xa73c, 4, 0x4, 0x924},
	{ 0xa74c, 1, 0x1c, 0x924},
	{ 0xa750, 1, 0x1c, 0xfff},
	{ 0xa754, 3, 0x1c, 0x924},
	{ 0xa760, 5, 0x4, 0x924},
	{ 0xa774, 7, 0x1c, 0x924},
	{ 0xa790, 15, 0x4, 0x924},
	{ 0xa7cc, 4, 0x1c, 0x924},
	{ 0xa7e0, 6, 0x18, 0x924},
	{ 0xa800, 18, 0x4, 0x924},
	{ 0xa848, 33, 0x1c, 0x924},
	{ 0xa8cc, 2, 0x18, 0x924},
	{ 0xa8d4, 4, 0x1c, 0x924},
	{ 0xa8e4, 1, 0x18, 0x924},
	{ 0xa8e8, 1, 0x1c, 0x924},
	{ 0xa8f0, 1, 0x1c, 0x924},
	{ 0xa8f8, 30, 0x18, 0x924},
	{ 0xa974, 73, 0x18, 0x924},
	{ 0xac30, 1, 0x18, 0x924},
	{ 0xac40, 1, 0x18, 0x924},
	{ 0xac50, 1, 0x18, 0x924},
	{ 0xac60, 1, 0x10, 0x924},
	{ 0x10000, 9, 0x1f, 0x924},
	{ 0x10024, 1, 0x7, 0x924},
	{ 0x10028, 5, 0x1f, 0x924},
	{ 0x1003c, 6, 0x7, 0x924},
	{ 0x10054, 20, 0x1f, 0x924},
	{ 0x100a4, 4, 0x7, 0x924},
	{ 0x100b4, 11, 0x1f, 0x924},
	{ 0x100e0, 4, 0x7, 0x924},
	{ 0x100f0, 8, 0x1f, 0x924},
	{ 0x10110, 6, 0x7, 0x924},
	{ 0x10128, 110, 0x1f, 0x924},
	{ 0x102e0, 4, 0x7, 0x924},
	{ 0x102f0, 18, 0x1f, 0x924},
	{ 0x10338, 20, 0x7, 0x924},
	{ 0x10388, 10, 0x1f, 0x924},
	{ 0x103d0, 2, 0x3, 0x1fff},
	{ 0x103dc, 1, 0x3, 0x1fff},
	{ 0x10400, 6, 0x7, 0x924},
	{ 0x10418, 1, 0x1f, 0xfff},
	{ 0x1041c, 1, 0x1f, 0x924},
	{ 0x10420, 1, 0x1f, 0xfff},
	{ 0x10424, 1, 0x1f, 0x924},
	{ 0x10428, 1, 0x1f, 0xfff},
	{ 0x1042c, 1, 0x1f, 0x924},
	{ 0x10430, 10, 0x7, 0x924},
	{ 0x10458, 2, 0x1f, 0x924},
	{ 0x10460, 1, 0x1f, 0xfff},
	{ 0x10464, 4, 0x1f, 0x924},
	{ 0x10474, 1, 0x1f, 0xfff},
	{ 0x10478, 14, 0x1f, 0x924},
	{ 0x104b0, 12, 0x7, 0x924},
	{ 0x104e0, 1, 0x1f, 0xfff},
	{ 0x104e8, 1, 0x1f, 0x924},
	{ 0x104ec, 1, 0x1f, 0xfff},
	{ 0x104f4, 1, 0x1f, 0x924},
	{ 0x104f8, 1, 0x1f, 0xfff},
	{ 0x10500, 2, 0x1f, 0x924},
	{ 0x10508, 1, 0x1f, 0xfff},
	{ 0x1050c, 9, 0x1f, 0x924},
	{ 0x10530, 1, 0x1f, 0xfff},
	{ 0x10534, 1, 0x1f, 0x924},
	{ 0x10538, 1, 0x1f, 0xfff},
	{ 0x1053c, 3, 0x1f, 0x924},
	{ 0x10548, 1, 0x1f, 0xfff},
	{ 0x1054c, 3, 0x1f, 0x924},
	{ 0x10558, 1, 0x1f, 0xfff},
	{ 0x1055c, 123, 0x1f, 0x924},
	{ 0x10750, 2, 0x7, 0x924},
	{ 0x10760, 2, 0x7, 0x924},
	{ 0x10770, 2, 0x7, 0x924},
	{ 0x10780, 2, 0x7, 0x924},
	{ 0x10790, 2, 0x1f, 0x924},
	{ 0x107a0, 2, 0x7, 0x924},
	{ 0x107b0, 2, 0x7, 0x924},
	{ 0x107c0, 2, 0x7, 0x924},
	{ 0x107d0, 2, 0x7, 0x924},
	{ 0x107e0, 2, 0x1f, 0x924},
	{ 0x10880, 2, 0x1f, 0x924},
	{ 0x10900, 2, 0x1f, 0x924},
	{ 0x16000, 1, 0x6, 0x924},
	{ 0x16004, 25, 0x1e, 0x924},
	{ 0x16070, 8, 0x1e, 0x924},
	{ 0x16090, 4, 0xe, 0x924},
	{ 0x160a0, 6, 0x1e, 0x924},
	{ 0x160c0, 7, 0x1e, 0x924},
	{ 0x160dc, 2, 0x6, 0x924},
	{ 0x160e4, 6, 0x1e, 0x924},
	{ 0x160fc, 4, 0x1e, 0x1fff},
	{ 0x1610c, 2, 0x6, 0x924},
	{ 0x16114, 6, 0x1e, 0x924},
	{ 0x16140, 48, 0x1e, 0x1fff},
	{ 0x16204, 5, 0x1e, 0x924},
	{ 0x18000, 1, 0x1e, 0x924},
	{ 0x18008, 1, 0x1e, 0x924},
	{ 0x18010, 35, 0x1c, 0x924},
	{ 0x180a4, 2, 0x1c, 0x924},
	{ 0x180c0, 9, 0x1c, 0x924},
	{ 0x180e4, 1, 0xc, 0x924},
	{ 0x180e8, 2, 0x1c, 0x924},
	{ 0x180f0, 1, 0xc, 0x924},
	{ 0x180f4, 79, 0x1c, 0x924},
	{ 0x18230, 1, 0xc, 0x924},
	{ 0x18234, 2, 0x1c, 0x924},
	{ 0x1823c, 1, 0xc, 0x924},
	{ 0x18240, 13, 0x1c, 0x924},
	{ 0x18274, 1, 0x4, 0x924},
	{ 0x18278, 12, 0x1c, 0x924},
	{ 0x182a8, 1, 0x1c, 0xfff},
	{ 0x182ac, 3, 0x1c, 0x924},
	{ 0x182b8, 1, 0x1c, 0xfff},
	{ 0x182bc, 19, 0x1c, 0x924},
	{ 0x18308, 1, 0x1c, 0xfff},
	{ 0x1830c, 3, 0x1c, 0x924},
	{ 0x18318, 1, 0x1c, 0xfff},
	{ 0x1831c, 7, 0x1c, 0x924},
	{ 0x18338, 1, 0x1c, 0xfff},
	{ 0x1833c, 3, 0x1c, 0x924},
	{ 0x18348, 1, 0x1c, 0xfff},
	{ 0x1834c, 28, 0x1c, 0x924},
	{ 0x183bc, 2, 0x1c, 0x1fff},
	{ 0x183c8, 3, 0x1c, 0x1fff},
	{ 0x183d8, 1, 0x1c, 0x1fff},
	{ 0x18440, 48, 0x1c, 0x1fff},
	{ 0x18500, 15, 0x1c, 0x924},
	{ 0x18570, 1, 0x18, 0xfff},
	{ 0x18574, 1, 0x18, 0x924},
	{ 0x18578, 1, 0x18, 0xfff},
	{ 0x1857c, 4, 0x18, 0x924},
	{ 0x1858c, 1, 0x18, 0xfff},
	{ 0x18590, 1, 0x18, 0x924},
	{ 0x18594, 1, 0x18, 0xfff},
	{ 0x18598, 32, 0x18, 0x924},
	{ 0x18618, 5, 0x10, 0x924},
	{ 0x1862c, 4, 0x10, 0xfff},
	{ 0x1863c, 16, 0x10, 0x924},
	{ 0x18680, 44, 0x10, 0x924},
	{ 0x18748, 12, 0x10, 0x924},
	{ 0x18788, 1, 0x10, 0x924},
	{ 0x1879c, 6, 0x10, 0x924},
	{ 0x187c4, 51, 0x10, 0x924},
	{ 0x18a00, 48, 0x10, 0x924},
	{ 0x20000, 24, 0x1f, 0x924},
	{ 0x20060, 8, 0x1f, 0x9e4},
	{ 0x20080, 94, 0x1f, 0x924},
	{ 0x201f8, 1, 0x3, 0x924},
	{ 0x201fc, 1, 0x1f, 0x924},
	{ 0x20200, 1, 0x3, 0x924},
	{ 0x20204, 1, 0x1f, 0x924},
	{ 0x20208, 1, 0x3, 0x924},
	{ 0x2020c, 4, 0x1f, 0x924},
	{ 0x2021c, 11, 0x1f, 0xfff},
	{ 0x20248, 24, 0x1f, 0x924},
	{ 0x202b8, 2, 0x1f, 0x1fff},
	{ 0x202c4, 1, 0x1f, 0x1fff},
	{ 0x202c8, 1, 0x1c, 0x924},
	{ 0x202d8, 4, 0x1c, 0x924},
	{ 0x202f0, 1, 0x10, 0x924},
	{ 0x20400, 1, 0x1f, 0x924},
	{ 0x20404, 1, 0x1f, 0xfff},
	{ 0x2040c, 2, 0x1f, 0xfff},
	{ 0x20414, 2, 0x1f, 0x924},
	{ 0x2041c, 2, 0x1f, 0xfff},
	{ 0x20424, 2, 0x1f, 0x924},
	{ 0x2042c, 18, 0x1e, 0x924},
	{ 0x20480, 1, 0x1f, 0x924},
	{ 0x20500, 1, 0x1f, 0x924},
	{ 0x20600, 1, 0x1f, 0x924},
	{ 0x28000, 1, 0x1f, 0x9e4},
	{ 0x28004, 255, 0x1f, 0x180},
	{ 0x28400, 1, 0x1f, 0x1c0},
	{ 0x28404, 255, 0x1f, 0x180},
	{ 0x28800, 1, 0x1f, 0x1c0},
	{ 0x28804, 255, 0x1f, 0x180},
	{ 0x28c00, 1, 0x1f, 0x1c0},
	{ 0x28c04, 255, 0x1f, 0x180},
	{ 0x29000, 1, 0x1f, 0x1c0},
	{ 0x29004, 255, 0x1f, 0x180},
	{ 0x29400, 1, 0x1f, 0x1c0},
	{ 0x29404, 255, 0x1f, 0x180},
	{ 0x29800, 1, 0x1f, 0x1c0},
	{ 0x29804, 255, 0x1f, 0x180},
	{ 0x29c00, 1, 0x1f, 0x1c0},
	{ 0x29c04, 255, 0x1f, 0x180},
	{ 0x2a000, 1, 0x1f, 0x1c0},
	{ 0x2a004, 255, 0x1f, 0x180},
	{ 0x2a400, 1, 0x1f, 0x1c0},
	{ 0x2a404, 255, 0x1f, 0x180},
	{ 0x2a800, 1, 0x1f, 0x1c0},
	{ 0x2a804, 255, 0x1f, 0x180},
	{ 0x2ac00, 1, 0x1f, 0x1c0},
	{ 0x2ac04, 255, 0x1f, 0x180},
	{ 0x2b000, 1, 0x1f, 0x1c0},
	{ 0x2b004, 255, 0x1f, 0x180},
	{ 0x2b400, 1, 0x1f, 0x1c0},
	{ 0x2b404, 255, 0x1f, 0x180},
	{ 0x2b800, 1, 0x1f, 0x1c0},
	{ 0x2b804, 255, 0x1f, 0x180},
	{ 0x2bc00, 1, 0x1f, 0x1c0},
	{ 0x2bc04, 255, 0x1f, 0x180},
	{ 0x2c000, 1, 0x1f, 0x1c0},
	{ 0x2c004, 255, 0x1f, 0x180},
	{ 0x2c400, 1, 0x1f, 0x1c0},
	{ 0x2c404, 255, 0x1f, 0x180},
	{ 0x2c800, 1, 0x1f, 0x1c0},
	{ 0x2c804, 255, 0x1f, 0x180},
	{ 0x2cc00, 1, 0x1f, 0x1c0},
	{ 0x2cc04, 255, 0x1f, 0x180},
	{ 0x2d000, 1, 0x1f, 0x1c0},
	{ 0x2d004, 255, 0x1f, 0x180},
	{ 0x2d400, 1, 0x1f, 0x1c0},
	{ 0x2d404, 255, 0x1f, 0x180},
	{ 0x2d800, 1, 0x1f, 0x1c0},
	{ 0x2d804, 255, 0x1f, 0x180},
	{ 0x2dc00, 1, 0x1f, 0x1c0},
	{ 0x2dc04, 255, 0x1f, 0x180},
	{ 0x2e000, 1, 0x1f, 0x1c0},
	{ 0x2e004, 255, 0x1f, 0x180},
	{ 0x2e400, 1, 0x1f, 0x1c0},
	{ 0x2e404, 255, 0x1f, 0x180},
	{ 0x2e800, 1, 0x1f, 0x1c0},
	{ 0x2e804, 255, 0x1f, 0x180},
	{ 0x2ec00, 1, 0x1f, 0x1c0},
	{ 0x2ec04, 255, 0x1f, 0x180},
	{ 0x2f000, 1, 0x1f, 0x1c0},
	{ 0x2f004, 255, 0x1f, 0x180},
	{ 0x2f400, 1, 0x1f, 0x1c0},
	{ 0x2f404, 255, 0x1f, 0x180},
	{ 0x2f800, 1, 0x1f, 0x1c0},
	{ 0x2f804, 255, 0x1f, 0x180},
	{ 0x2fc00, 1, 0x1f, 0x1c0},
	{ 0x2fc04, 255, 0x1f, 0x180},
	{ 0x30000, 1, 0x1f, 0x9e4},
	{ 0x30004, 255, 0x1f, 0x180},
	{ 0x30400, 1, 0x1f, 0x1c0},
	{ 0x30404, 255, 0x1f, 0x180},
	{ 0x30800, 1, 0x1f, 0x1c0},
	{ 0x30804, 255, 0x1f, 0x180},
	{ 0x30c00, 1, 0x1f, 0x1c0},
	{ 0x30c04, 255, 0x1f, 0x180},
	{ 0x31000, 1, 0x1f, 0x1c0},
	{ 0x31004, 255, 0x1f, 0x180},
	{ 0x31400, 1, 0x1f, 0x1c0},
	{ 0x31404, 255, 0x1f, 0x180},
	{ 0x31800, 1, 0x1f, 0x1c0},
	{ 0x31804, 255, 0x1f, 0x180},
	{ 0x31c00, 1, 0x1f, 0x1c0},
	{ 0x31c04, 255, 0x1f, 0x180},
	{ 0x32000, 1, 0x1f, 0x1c0},
	{ 0x32004, 255, 0x1f, 0x180},
	{ 0x32400, 1, 0x1f, 0x1c0},
	{ 0x32404, 255, 0x1f, 0x180},
	{ 0x32800, 1, 0x1f, 0x1c0},
	{ 0x32804, 255, 0x1f, 0x180},
	{ 0x32c00, 1, 0x1f, 0x1c0},
	{ 0x32c04, 255, 0x1f, 0x180},
	{ 0x33000, 1, 0x1f, 0x1c0},
	{ 0x33004, 255, 0x1f, 0x180},
	{ 0x33400, 1, 0x1f, 0x1c0},
	{ 0x33404, 255, 0x1f, 0x180},
	{ 0x33800, 1, 0x1f, 0x1c0},
	{ 0x33804, 255, 0x1f, 0x180},
	{ 0x33c00, 1, 0x1f, 0x1c0},
	{ 0x33c04, 255, 0x1f, 0x180},
	{ 0x34000, 1, 0x1f, 0x1c0},
	{ 0x34004, 255, 0x1f, 0x180},
	{ 0x34400, 1, 0x1f, 0x1c0},
	{ 0x34404, 255, 0x1f, 0x180},
	{ 0x34800, 1, 0x1f, 0x1c0},
	{ 0x34804, 255, 0x1f, 0x180},
	{ 0x34c00, 1, 0x1f, 0x1c0},
	{ 0x34c04, 255, 0x1f, 0x180},
	{ 0x35000, 1, 0x1f, 0x1c0},
	{ 0x35004, 255, 0x1f, 0x180},
	{ 0x35400, 1, 0x1f, 0x1c0},
	{ 0x35404, 255, 0x1f, 0x180},
	{ 0x35800, 1, 0x1f, 0x1c0},
	{ 0x35804, 255, 0x1f, 0x180},
	{ 0x35c00, 1, 0x1f, 0x1c0},
	{ 0x35c04, 255, 0x1f, 0x180},
	{ 0x36000, 1, 0x1f, 0x1c0},
	{ 0x36004, 255, 0x1f, 0x180},
	{ 0x36400, 1, 0x1f, 0x1c0},
	{ 0x36404, 255, 0x1f, 0x180},
	{ 0x36800, 1, 0x1f, 0x1c0},
	{ 0x36804, 255, 0x1f, 0x180},
	{ 0x36c00, 1, 0x1f, 0x1c0},
	{ 0x36c04, 255, 0x1f, 0x180},
	{ 0x37000, 1, 0x1f, 0x1c0},
	{ 0x37004, 255, 0x1f, 0x180},
	{ 0x37400, 1, 0x1f, 0x1c0},
	{ 0x37404, 255, 0x1f, 0x180},
	{ 0x37800, 1, 0x1f, 0x1c0},
	{ 0x37804, 255, 0x1f, 0x180},
	{ 0x37c00, 1, 0x1f, 0x1c0},
	{ 0x37c04, 255, 0x1f, 0x180},
	{ 0x38000, 1, 0x1f, 0x1c0},
	{ 0x38004, 255, 0x1f, 0x180},
	{ 0x38400, 1, 0x1f, 0x1c0},
	{ 0x38404, 255, 0x1f, 0x180},
	{ 0x38800, 1, 0x1f, 0x1c0},
	{ 0x38804, 255, 0x1f, 0x180},
	{ 0x38c00, 1, 0x1f, 0x1c0},
	{ 0x38c04, 255, 0x1f, 0x180},
	{ 0x39000, 1, 0x1f, 0x1c0},
	{ 0x39004, 255, 0x1f, 0x180},
	{ 0x39400, 1, 0x1f, 0x1c0},
	{ 0x39404, 255, 0x1f, 0x180},
	{ 0x39800, 1, 0x1f, 0x1c0},
	{ 0x39804, 255, 0x1f, 0x180},
	{ 0x39c00, 1, 0x1f, 0x1c0},
	{ 0x39c04, 255, 0x1f, 0x180},
	{ 0x3a000, 1, 0x1f, 0x1c0},
	{ 0x3a004, 255, 0x1f, 0x180},
	{ 0x3a400, 1, 0x1f, 0x1c0},
	{ 0x3a404, 255, 0x1f, 0x180},
	{ 0x3a800, 1, 0x1f, 0x1c0},
	{ 0x3a804, 255, 0x1f, 0x180},
	{ 0x3ac00, 1, 0x1f, 0x1c0},
	{ 0x3ac04, 255, 0x1f, 0x180},
	{ 0x3b000, 1, 0x1f, 0x1c0},
	{ 0x3b004, 255, 0x1f, 0x180},
	{ 0x3b400, 1, 0x1f, 0x1c0},
	{ 0x3b404, 255, 0x1f, 0x180},
	{ 0x3b800, 1, 0x1f, 0x1c0},
	{ 0x3b804, 255, 0x1f, 0x180},
	{ 0x3bc00, 1, 0x1f, 0x1c0},
	{ 0x3bc04, 255, 0x1f, 0x180},
	{ 0x3c000, 1, 0x1f, 0x1c0},
	{ 0x3c004, 255, 0x1f, 0x180},
	{ 0x3c400, 1, 0x1f, 0x1c0},
	{ 0x3c404, 255, 0x1f, 0x180},
	{ 0x3c800, 1, 0x1f, 0x1c0},
	{ 0x3c804, 255, 0x1f, 0x180},
	{ 0x3cc00, 1, 0x1f, 0x1c0},
	{ 0x3cc04, 255, 0x1f, 0x180},
	{ 0x3d000, 1, 0x1f, 0x1c0},
	{ 0x3d004, 255, 0x1f, 0x180},
	{ 0x3d400, 1, 0x1f, 0x1c0},
	{ 0x3d404, 255, 0x1f, 0x180},
	{ 0x3d800, 1, 0x1f, 0x1c0},
	{ 0x3d804, 255, 0x1f, 0x180},
	{ 0x3dc00, 1, 0x1f, 0x1c0},
	{ 0x3dc04, 255, 0x1f, 0x180},
	{ 0x3e000, 1, 0x1f, 0x1c0},
	{ 0x3e004, 255, 0x1f, 0x180},
	{ 0x3e400, 1, 0x1f, 0x1c0},
	{ 0x3e404, 255, 0x1f, 0x180},
	{ 0x3e800, 1, 0x1f, 0x1c0},
	{ 0x3e804, 255, 0x1f, 0x180},
	{ 0x3ec00, 1, 0x1f, 0x1c0},
	{ 0x3ec04, 255, 0x1f, 0x180},
	{ 0x3f000, 1, 0x1f, 0x1c0},
	{ 0x3f004, 255, 0x1f, 0x180},
	{ 0x3f400, 1, 0x1f, 0x1c0},
	{ 0x3f404, 255, 0x1f, 0x180},
	{ 0x3f800, 1, 0x1f, 0x1c0},
	{ 0x3f804, 255, 0x1f, 0x180},
	{ 0x3fc00, 1, 0x1f, 0x1c0},
	{ 0x3fc04, 255, 0x1f, 0x180},
	{ 0x40000, 85, 0x1f, 0x924},
	{ 0x40154, 13, 0x1f, 0xfff},
	{ 0x40198, 2, 0x1f, 0x1fff},
	{ 0x401a4, 1, 0x1f, 0x1fff},
	{ 0x401a8, 8, 0x1e, 0x924},
	{ 0x401c8, 1, 0x2, 0x924},
	{ 0x401cc, 2, 0x1e, 0x924},
	{ 0x401d4, 2, 0x1c, 0x924},
	{ 0x40200, 4, 0x1f, 0x924},
	{ 0x40220, 6, 0x1c, 0x924},
	{ 0x40238, 8, 0xc, 0x924},
	{ 0x40258, 4, 0x1c, 0x924},
	{ 0x40268, 2, 0x18, 0x924},
	{ 0x40270, 17, 0x10, 0x924},
	{ 0x40400, 43, 0x1f, 0x924},
	{ 0x404bc, 2, 0x1f, 0x1fff},
	{ 0x404c8, 1, 0x1f, 0x1fff},
	{ 0x404cc, 3, 0x1e, 0x924},
	{ 0x404e0, 1, 0x1c, 0x924},
	{ 0x40500, 2, 0x1f, 0x924},
	{ 0x40510, 2, 0x1f, 0x924},
	{ 0x40520, 2, 0x1f, 0x924},
	{ 0x40530, 2, 0x1f, 0x924},
	{ 0x40540, 2, 0x1f, 0x924},
	{ 0x40550, 10, 0x1c, 0x924},
	{ 0x40610, 2, 0x1c, 0x924},
	{ 0x42000, 164, 0x1f, 0x924},
	{ 0x422b0, 2, 0x1f, 0x1fff},
	{ 0x422bc, 1, 0x1f, 0x1fff},
	{ 0x422c0, 4, 0x1c, 0x924},
	{ 0x422d4, 5, 0x1e, 0x924},
	{ 0x422e8, 1, 0x1c, 0x924},
	{ 0x42400, 49, 0x1f, 0x924},
	{ 0x424c8, 32, 0x1f, 0x924},
	{ 0x42548, 1, 0x1f, 0xfff},
	{ 0x4254c, 1, 0x1f, 0x924},
	{ 0x42550, 1, 0x1f, 0xfff},
	{ 0x42554, 1, 0x1f, 0x924},
	{ 0x42558, 1, 0x1f, 0xfff},
	{ 0x4255c, 1, 0x1f, 0x924},
	{ 0x42568, 2, 0x1f, 0x924},
	{ 0x42640, 5, 0x1c, 0x924},
	{ 0x42800, 1, 0x1f, 0x924},
	{ 0x50000, 1, 0x1f, 0x1fff},
	{ 0x50004, 19, 0x1f, 0x924},
	{ 0x50050, 8, 0x1f, 0x93c},
	{ 0x50070, 60, 0x1f, 0x924},
	{ 0x50160, 8, 0x1f, 0xfff},
	{ 0x50180, 20, 0x1f, 0x924},
	{ 0x501e0, 2, 0x1f, 0x1fff},
	{ 0x501ec, 1, 0x1f, 0x1fff},
	{ 0x501f0, 4, 0x1e, 0x924},
	{ 0x50200, 1, 0x1f, 0x924},
	{ 0x50204, 1, 0x1f, 0xfff},
	{ 0x5020c, 2, 0x1f, 0xfff},
	{ 0x50214, 2, 0x1f, 0x924},
	{ 0x5021c, 1, 0x1f, 0xfff},
	{ 0x50220, 2, 0x1f, 0x924},
	{ 0x50228, 6, 0x1e, 0x924},
	{ 0x50240, 1, 0x1f, 0x924},
	{ 0x50280, 1, 0x1f, 0x924},
	{ 0x50300, 1, 0x1c, 0x924},
	{ 0x5030c, 1, 0x1c, 0x924},
	{ 0x50318, 1, 0x1c, 0x934},
	{ 0x5031c, 1, 0x1c, 0x924},
	{ 0x50320, 2, 0x1c, 0x934},
	{ 0x50330, 1, 0x10, 0x924},
	{ 0x52000, 1, 0x1f, 0x924},
	{ 0x54000, 1, 0x1f, 0x93c},
	{ 0x54004, 255, 0x1f, 0x30},
	{ 0x54400, 1, 0x1f, 0x38},
	{ 0x54404, 255, 0x1f, 0x30},
	{ 0x54800, 1, 0x1f, 0x38},
	{ 0x54804, 255, 0x1f, 0x30},
	{ 0x54c00, 1, 0x1f, 0x38},
	{ 0x54c04, 255, 0x1f, 0x30},
	{ 0x55000, 1, 0x1f, 0x38},
	{ 0x55004, 255, 0x1f, 0x30},
	{ 0x55400, 1, 0x1f, 0x38},
	{ 0x55404, 255, 0x1f, 0x30},
	{ 0x55800, 1, 0x1f, 0x38},
	{ 0x55804, 255, 0x1f, 0x30},
	{ 0x55c00, 1, 0x1f, 0x38},
	{ 0x55c04, 255, 0x1f, 0x30},
	{ 0x56000, 1, 0x1f, 0x38},
	{ 0x56004, 255, 0x1f, 0x30},
	{ 0x56400, 1, 0x1f, 0x38},
	{ 0x56404, 255, 0x1f, 0x30},
	{ 0x56800, 1, 0x1f, 0x38},
	{ 0x56804, 255, 0x1f, 0x30},
	{ 0x56c00, 1, 0x1f, 0x38},
	{ 0x56c04, 255, 0x1f, 0x30},
	{ 0x57000, 1, 0x1f, 0x38},
	{ 0x57004, 255, 0x1f, 0x30},
	{ 0x58000, 1, 0x1f, 0x934},
	{ 0x58004, 8191, 0x3, 0x30},
	{ 0x60000, 26, 0x1f, 0x924},
	{ 0x60068, 8, 0x3, 0x924},
	{ 0x60088, 2, 0x1f, 0x924},
	{ 0x60090, 1, 0x1f, 0xfff},
	{ 0x60094, 9, 0x1f, 0x924},
	{ 0x600b8, 9, 0x3, 0x924},
	{ 0x600dc, 1, 0x1f, 0x924},
	{ 0x600e0, 5, 0x3, 0x924},
	{ 0x600f4, 1, 0x7, 0x924},
	{ 0x600f8, 1, 0x3, 0x924},
	{ 0x600fc, 8, 0x1f, 0x924},
	{ 0x6012c, 2, 0x1f, 0x1fff},
	{ 0x60138, 1, 0x1f, 0x1fff},
	{ 0x6013c, 24, 0x2, 0x924},
	{ 0x6019c, 2, 0x1c, 0x924},
	{ 0x601ac, 18, 0x1c, 0x924},
	{ 0x60200, 1, 0x1f, 0xb6d},
	{ 0x60204, 2, 0x1f, 0x249},
	{ 0x60210, 13, 0x1c, 0x924},
	{ 0x60244, 16, 0x10, 0x924},
	{ 0x61000, 1, 0x1f, 0xb6d},
	{ 0x61004, 511, 0x1f, 0x249},
	{ 0x61800, 512, 0x18, 0x249},
	{ 0x70000, 8, 0x1f, 0xb6d},
	{ 0x70020, 8184, 0x1f, 0x249},
	{ 0x78000, 8192, 0x18, 0x249},
	{ 0x85000, 3, 0x1f, 0x1000},
	{ 0x8501c, 7, 0x1f, 0x1000},
	{ 0x85048, 1, 0x1f, 0x1000},
	{ 0x85200, 32, 0x1f, 0x1000},
	{ 0xa0000, 16384, 0x3, 0x1000},
	{ 0xb0000, 16384, 0x2, 0x1000},
	{ 0xc1000, 7, 0x1f, 0x924},
	{ 0xc102c, 2, 0x1f, 0x1fff},
	{ 0xc1038, 1, 0x1f, 0x1fff},
	{ 0xc103c, 2, 0x1c, 0x924},
	{ 0xc1800, 2, 0x1f, 0x924},
	{ 0xc2000, 164, 0x1f, 0x924},
	{ 0xc22b0, 2, 0x1f, 0x1fff},
	{ 0xc22bc, 1, 0x1f, 0x1fff},
	{ 0xc22c0, 5, 0x1c, 0x924},
	{ 0xc22d8, 4, 0x1c, 0x924},
	{ 0xc2400, 49, 0x1f, 0x924},
	{ 0xc24c8, 32, 0x1f, 0x924},
	{ 0xc2548, 1, 0x1f, 0xfff},
	{ 0xc254c, 1, 0x1f, 0x924},
	{ 0xc2550, 1, 0x1f, 0xfff},
	{ 0xc2554, 1, 0x1f, 0x924},
	{ 0xc2558, 1, 0x1f, 0xfff},
	{ 0xc255c, 1, 0x1f, 0x924},
	{ 0xc2568, 2, 0x1f, 0x924},
	{ 0xc2600, 1, 0x1f, 0x924},
	{ 0xc4000, 165, 0x1f, 0x924},
	{ 0xc42b4, 2, 0x1f, 0x1fff},
	{ 0xc42c0, 1, 0x1f, 0x1fff},
	{ 0xc42d8, 2, 0x1c, 0x924},
	{ 0xc42e0, 7, 0x1e, 0x924},
	{ 0xc42fc, 1, 0x1c, 0x924},
	{ 0xc4400, 51, 0x1f, 0x924},
	{ 0xc44d0, 32, 0x1f, 0x924},
	{ 0xc4550, 1, 0x1f, 0xfff},
	{ 0xc4554, 1, 0x1f, 0x924},
	{ 0xc4558, 1, 0x1f, 0xfff},
	{ 0xc455c, 1, 0x1f, 0x924},
	{ 0xc4560, 1, 0x1f, 0xfff},
	{ 0xc4564, 1, 0x1f, 0x924},
	{ 0xc4570, 2, 0x1f, 0x924},
	{ 0xc4578, 5, 0x1c, 0x924},
	{ 0xc4600, 1, 0x1f, 0x924},
	{ 0xd0000, 19, 0x1f, 0x924},
	{ 0xd004c, 8, 0x1f, 0x1927},
	{ 0xd006c, 64, 0x1f, 0x924},
	{ 0xd016c, 8, 0x1f, 0xfff},
	{ 0xd018c, 19, 0x1f, 0x924},
	{ 0xd01e8, 2, 0x1f, 0x1fff},
	{ 0xd01f4, 1, 0x1f, 0x1fff},
	{ 0xd01fc, 1, 0x1c, 0x924},
	{ 0xd0200, 1, 0x1f, 0x924},
	{ 0xd0204, 1, 0x1f, 0xfff},
	{ 0xd020c, 3, 0x1f, 0xfff},
	{ 0xd0218, 4, 0x1f, 0x924},
	{ 0xd0228, 18, 0x1e, 0x924},
	{ 0xd0280, 1, 0x1f, 0x924},
	{ 0xd0300, 1, 0x1f, 0x924},
	{ 0xd0400, 1, 0x1f, 0x924},
	{ 0xd0818, 1, 0x10, 0x924},
	{ 0xd4000, 1, 0x1f, 0x1927},
	{ 0xd4004, 255, 0x1f, 0x6},
	{ 0xd4400, 1, 0x1f, 0x1007},
	{ 0xd4404, 255, 0x1f, 0x6},
	{ 0xd4800, 1, 0x1f, 0x1007},
	{ 0xd4804, 255, 0x1f, 0x6},
	{ 0xd4c00, 1, 0x1f, 0x1007},
	{ 0xd4c04, 255, 0x1f, 0x6},
	{ 0xd5000, 1, 0x1f, 0x1007},
	{ 0xd5004, 255, 0x1f, 0x6},
	{ 0xd5400, 1, 0x1f, 0x1007},
	{ 0xd5404, 255, 0x1f, 0x6},
	{ 0xd5800, 1, 0x1f, 0x1007},
	{ 0xd5804, 255, 0x1f, 0x6},
	{ 0xd5c00, 1, 0x1f, 0x1007},
	{ 0xd5c04, 255, 0x1f, 0x6},
	{ 0xd6000, 1, 0x1f, 0x1007},
	{ 0xd6004, 255, 0x1f, 0x6},
	{ 0xd6400, 1, 0x1f, 0x1007},
	{ 0xd6404, 255, 0x1f, 0x6},
	{ 0xd8000, 1, 0x1f, 0x1927},
	{ 0xd8004, 255, 0x1f, 0x6},
	{ 0xd8400, 1, 0x1f, 0x1007},
	{ 0xd8404, 255, 0x1f, 0x6},
	{ 0xd8800, 1, 0x1f, 0x1007},
	{ 0xd8804, 255, 0x1f, 0x6},
	{ 0xd8c00, 1, 0x1f, 0x1007},
	{ 0xd8c04, 255, 0x1f, 0x6},
	{ 0xd9000, 1, 0x1f, 0x1007},
	{ 0xd9004, 255, 0x1f, 0x6},
	{ 0xd9400, 1, 0x1f, 0x1007},
	{ 0xd9404, 255, 0x1f, 0x6},
	{ 0xd9800, 1, 0x1f, 0x1007},
	{ 0xd9804, 255, 0x1f, 0x6},
	{ 0xd9c00, 1, 0x1f, 0x1007},
	{ 0xd9c04, 255, 0x1f, 0x6},
	{ 0xda000, 1, 0x1f, 0x1007},
	{ 0xda004, 255, 0x1f, 0x6},
	{ 0xda400, 1, 0x1f, 0x1007},
	{ 0xda404, 255, 0x1f, 0x6},
	{ 0xda800, 1, 0x1f, 0x1007},
	{ 0xda804, 255, 0x1f, 0x6},
	{ 0xdac00, 1, 0x1f, 0x1007},
	{ 0xdac04, 255, 0x1f, 0x6},
	{ 0xdb000, 1, 0x1f, 0x1007},
	{ 0xdb004, 255, 0x1f, 0x6},
	{ 0xdb400, 1, 0x1f, 0x1007},
	{ 0xdb404, 255, 0x1f, 0x6},
	{ 0xdb800, 1, 0x1f, 0x1007},
	{ 0xdb804, 255, 0x1f, 0x6},
	{ 0xdbc00, 1, 0x1f, 0x1007},
	{ 0xdbc04, 255, 0x1f, 0x6},
	{ 0xdc000, 1, 0x1f, 0x1007},
	{ 0xdc004, 255, 0x1f, 0x6},
	{ 0xdc400, 1, 0x1f, 0x1007},
	{ 0xdc404, 255, 0x1f, 0x6},
	{ 0xdc800, 1, 0x1f, 0x1007},
	{ 0xdc804, 255, 0x1f, 0x6},
	{ 0xdcc00, 1, 0x1f, 0x1007},
	{ 0xdcc04, 255, 0x1f, 0x6},
	{ 0xdd000, 1, 0x1f, 0x1007},
	{ 0xdd004, 255, 0x1f, 0x6},
	{ 0xdd400, 1, 0x1f, 0x1007},
	{ 0xdd404, 255, 0x1f, 0x6},
	{ 0xdd800, 1, 0x1f, 0x1007},
	{ 0xdd804, 255, 0x1f, 0x6},
	{ 0xddc00, 1, 0x1f, 0x1007},
	{ 0xddc04, 255, 0x1f, 0x6},
	{ 0xde000, 1, 0x1f, 0x1007},
	{ 0xde004, 255, 0x1f, 0x6},
	{ 0xde400, 1, 0x1f, 0x1007},
	{ 0xde404, 255, 0x1f, 0x6},
	{ 0xde800, 1, 0x1f, 0x1007},
	{ 0xde804, 255, 0x1f, 0x6},
	{ 0xdec00, 1, 0x1f, 0x1007},
	{ 0xdec04, 255, 0x1f, 0x6},
	{ 0xdf000, 1, 0x1f, 0x1007},
	{ 0xdf004, 255, 0x1f, 0x6},
	{ 0xdf400, 1, 0x1f, 0x1007},
	{ 0xdf404, 255, 0x1f, 0x6},
	{ 0xdf800, 1, 0x1f, 0x1007},
	{ 0xdf804, 255, 0x1f, 0x6},
	{ 0xdfc00, 1, 0x1f, 0x1007},
	{ 0xdfc04, 255, 0x1f, 0x6},
	{ 0xe0000, 21, 0x1f, 0x924},
	{ 0xe0054, 8, 0x1f, 0xf24},
	{ 0xe0074, 49, 0x1f, 0x924},
	{ 0xe0138, 1, 0x3, 0x924},
	{ 0xe013c, 6, 0x1f, 0x924},
	{ 0xe0154, 8, 0x1f, 0xfff},
	{ 0xe0174, 21, 0x1f, 0x924},
	{ 0xe01d8, 2, 0x1f, 0x1fff},
	{ 0xe01e4, 1, 0x1f, 0x1fff},
	{ 0xe01f4, 1, 0x4, 0x924},
	{ 0xe01f8, 1, 0x1c, 0x924},
	{ 0xe0200, 1, 0x1f, 0x924},
	{ 0xe0204, 1, 0x1f, 0xfff},
	{ 0xe020c, 2, 0x1f, 0xfff},
	{ 0xe0214, 2, 0x1f, 0x924},
	{ 0xe021c, 2, 0x1f, 0xfff},
	{ 0xe0224, 2, 0x1f, 0x924},
	{ 0xe022c, 18, 0x1e, 0x924},
	{ 0xe0280, 1, 0x1f, 0x924},
	{ 0xe0300, 1, 0x1f, 0x924},
	{ 0xe0400, 1, 0x10, 0x924},
	{ 0xe1000, 1, 0x1f, 0x924},
	{ 0xe2000, 1, 0x1f, 0xf24},
	{ 0xe2004, 255, 0x1f, 0xc00},
	{ 0xe2400, 1, 0x1f, 0xe00},
	{ 0xe2404, 255, 0x1f, 0xc00},
	{ 0xe2800, 1, 0x1f, 0xe00},
	{ 0xe2804, 255, 0x1f, 0xc00},
	{ 0xe2c00, 1, 0x1f, 0xe00},
	{ 0xe2c04, 255, 0x1f, 0xc00},
	{ 0xe3000, 1, 0x1f, 0xe00},
	{ 0xe3004, 255, 0x1f, 0xc00},
	{ 0xe3400, 1, 0x1f, 0xe00},
	{ 0xe3404, 255, 0x1f, 0xc00},
	{ 0xe3800, 1, 0x1f, 0xe00},
	{ 0xe3804, 255, 0x1f, 0xc00},
	{ 0xe3c00, 1, 0x1f, 0xe00},
	{ 0xe3c04, 255, 0x1f, 0xc00},
	{ 0xf0000, 1, 0x1f, 0xf24},
	{ 0xf0004, 255, 0x1f, 0xc00},
	{ 0xf0400, 1, 0x1f, 0xe00},
	{ 0xf0404, 255, 0x1f, 0xc00},
	{ 0xf0800, 1, 0x1f, 0xe00},
	{ 0xf0804, 255, 0x1f, 0xc00},
	{ 0xf0c00, 1, 0x1f, 0xe00},
	{ 0xf0c04, 255, 0x1f, 0xc00},
	{ 0xf1000, 1, 0x1f, 0xe00},
	{ 0xf1004, 255, 0x1f, 0xc00},
	{ 0xf1400, 1, 0x1f, 0xe00},
	{ 0xf1404, 255, 0x1f, 0xc00},
	{ 0xf1800, 1, 0x1f, 0xe00},
	{ 0xf1804, 255, 0x1f, 0xc00},
	{ 0xf1c00, 1, 0x1f, 0xe00},
	{ 0xf1c04, 255, 0x1f, 0xc00},
	{ 0xf2000, 1, 0x1f, 0xe00},
	{ 0xf2004, 255, 0x1f, 0xc00},
	{ 0xf2400, 1, 0x1f, 0xe00},
	{ 0xf2404, 255, 0x1f, 0xc00},
	{ 0xf2800, 1, 0x1f, 0xe00},
	{ 0xf2804, 255, 0x1f, 0xc00},
	{ 0xf2c00, 1, 0x1f, 0xe00},
	{ 0xf2c04, 255, 0x1f, 0xc00},
	{ 0xf3000, 1, 0x1f, 0xe00},
	{ 0xf3004, 255, 0x1f, 0xc00},
	{ 0xf3400, 1, 0x1f, 0xe00},
	{ 0xf3404, 255, 0x1f, 0xc00},
	{ 0xf3800, 1, 0x1f, 0xe00},
	{ 0xf3804, 255, 0x1f, 0xc00},
	{ 0xf3c00, 1, 0x1f, 0xe00},
	{ 0xf3c04, 255, 0x1f, 0xc00},
	{ 0xf4000, 1, 0x1f, 0xe00},
	{ 0xf4004, 255, 0x1f, 0xc00},
	{ 0xf4400, 1, 0x1f, 0xe00},
	{ 0xf4404, 255, 0x1f, 0xc00},
	{ 0xf4800, 1, 0x1f, 0xe00},
	{ 0xf4804, 255, 0x1f, 0xc00},
	{ 0xf4c00, 1, 0x1f, 0xe00},
	{ 0xf4c04, 255, 0x1f, 0xc00},
	{ 0xf5000, 1, 0x1f, 0xe00},
	{ 0xf5004, 255, 0x1f, 0xc00},
	{ 0xf5400, 1, 0x1f, 0xe00},
	{ 0xf5404, 255, 0x1f, 0xc00},
	{ 0xf5800, 1, 0x1f, 0xe00},
	{ 0xf5804, 255, 0x1f, 0xc00},
	{ 0xf5c00, 1, 0x1f, 0xe00},
	{ 0xf5c04, 255, 0x1f, 0xc00},
	{ 0xf6000, 1, 0x1f, 0xe00},
	{ 0xf6004, 255, 0x1f, 0xc00},
	{ 0xf6400, 1, 0x1f, 0xe00},
	{ 0xf6404, 255, 0x1f, 0xc00},
	{ 0xf6800, 1, 0x1f, 0xe00},
	{ 0xf6804, 255, 0x1f, 0xc00},
	{ 0xf6c00, 1, 0x1f, 0xe00},
	{ 0xf6c04, 255, 0x1f, 0xc00},
	{ 0xf7000, 1, 0x1f, 0xe00},
	{ 0xf7004, 255, 0x1f, 0xc00},
	{ 0xf7400, 1, 0x1f, 0xe00},
	{ 0xf7404, 255, 0x1f, 0xc00},
	{ 0xf7800, 1, 0x1f, 0xe00},
	{ 0xf7804, 255, 0x1f, 0xc00},
	{ 0xf7c00, 1, 0x1f, 0xe00},
	{ 0xf7c04, 255, 0x1f, 0xc00},
	{ 0xf8000, 1, 0x1f, 0xe00},
	{ 0xf8004, 255, 0x1f, 0xc00},
	{ 0xf8400, 1, 0x1f, 0xe00},
	{ 0xf8404, 255, 0x1f, 0xc00},
	{ 0xf8800, 1, 0x1f, 0xe00},
	{ 0xf8804, 255, 0x1f, 0xc00},
	{ 0xf8c00, 1, 0x1f, 0xe00},
	{ 0xf8c04, 255, 0x1f, 0xc00},
	{ 0xf9000, 1, 0x1f, 0xe00},
	{ 0xf9004, 255, 0x1f, 0xc00},
	{ 0xf9400, 1, 0x1f, 0xe00},
	{ 0xf9404, 255, 0x1f, 0xc00},
	{ 0xf9800, 1, 0x1f, 0xe00},
	{ 0xf9804, 255, 0x1f, 0xc00},
	{ 0xf9c00, 1, 0x1f, 0xe00},
	{ 0xf9c04, 255, 0x1f, 0xc00},
	{ 0xfa000, 1, 0x1f, 0xe00},
	{ 0xfa004, 255, 0x1f, 0xc00},
	{ 0xfa400, 1, 0x1f, 0xe00},
	{ 0xfa404, 255, 0x1f, 0xc00},
	{ 0xfa800, 1, 0x1f, 0xe00},
	{ 0xfa804, 255, 0x1f, 0xc00},
	{ 0xfac00, 1, 0x1f, 0xe00},
	{ 0xfac04, 255, 0x1f, 0xc00},
	{ 0xfb000, 1, 0x1f, 0xe00},
	{ 0xfb004, 255, 0x1f, 0xc00},
	{ 0xfb400, 1, 0x1f, 0xe00},
	{ 0xfb404, 255, 0x1f, 0xc00},
	{ 0xfb800, 1, 0x1f, 0xe00},
	{ 0xfb804, 255, 0x1f, 0xc00},
	{ 0xfbc00, 1, 0x1f, 0xe00},
	{ 0xfbc04, 255, 0x1f, 0xc00},
	{ 0xfc000, 1, 0x1f, 0xe00},
	{ 0xfc004, 255, 0x1f, 0xc00},
	{ 0xfc400, 1, 0x1f, 0xe00},
	{ 0xfc404, 255, 0x1f, 0xc00},
	{ 0xfc800, 1, 0x1f, 0xe00},
	{ 0xfc804, 255, 0x1f, 0xc00},
	{ 0xfcc00, 1, 0x1f, 0xe00},
	{ 0xfcc04, 255, 0x1f, 0xc00},
	{ 0xfd000, 1, 0x1f, 0xe00},
	{ 0xfd004, 255, 0x1f, 0xc00},
	{ 0xfd400, 1, 0x1f, 0xe00},
	{ 0xfd404, 255, 0x1f, 0xc00},
	{ 0xfd800, 1, 0x1f, 0xe00},
	{ 0xfd804, 255, 0x1f, 0xc00},
	{ 0xfdc00, 1, 0x1f, 0xe00},
	{ 0xfdc04, 255, 0x1f, 0xc00},
	{ 0xfe000, 1, 0x1f, 0xe00},
	{ 0xfe004, 255, 0x1f, 0xc00},
	{ 0xfe400, 1, 0x1f, 0xe00},
	{ 0xfe404, 255, 0x1f, 0xc00},
	{ 0xfe800, 1, 0x1f, 0xe00},
	{ 0xfe804, 255, 0x1f, 0xc00},
	{ 0xfec00, 1, 0x1f, 0xe00},
	{ 0xfec04, 255, 0x1f, 0xc00},
	{ 0xff000, 1, 0x1f, 0xe00},
	{ 0xff004, 255, 0x1f, 0xc00},
	{ 0xff400, 1, 0x1f, 0xe00},
	{ 0xff404, 255, 0x1f, 0xc00},
	{ 0xff800, 1, 0x1f, 0xe00},
	{ 0xff804, 255, 0x1f, 0xc00},
	{ 0xffc00, 1, 0x1f, 0xe00},
	{ 0xffc04, 255, 0x1f, 0xc00},
	{ 0x101000, 5, 0x1f, 0x924},
	{ 0x101014, 1, 0x1f, 0xfff},
	{ 0x101018, 6, 0x1f, 0x924},
	{ 0x101040, 2, 0x1f, 0x1fff},
	{ 0x10104c, 1, 0x1f, 0x1fff},
	{ 0x101050, 1, 0x1e, 0x924},
	{ 0x101054, 3, 0x1c, 0x924},
	{ 0x101100, 1, 0x1f, 0x924},
	{ 0x101800, 8, 0x1f, 0x924},
	{ 0x102000, 18, 0x1f, 0x924},
	{ 0x102058, 2, 0x1f, 0x1fff},
	{ 0x102064, 1, 0x1f, 0x1fff},
	{ 0x102068, 6, 0x1c, 0x924},
	{ 0x102080, 16, 0x1f, 0xfff},
	{ 0x1020c0, 1, 0x1f, 0x924},
	{ 0x1020c8, 8, 0x2, 0x924},
	{ 0x1020e8, 9, 0x1c, 0x924},
	{ 0x102400, 1, 0x1f, 0x924},
	{ 0x103000, 1, 0x1f, 0x924},
	{ 0x103004, 2, 0x1f, 0xfff},
	{ 0x10300c, 23, 0x1f, 0x924},
	{ 0x103088, 2, 0x1f, 0x1fff},
	{ 0x103094, 1, 0x1f, 0x1fff},
	{ 0x103098, 1, 0x1e, 0x924},
	{ 0x10309c, 2, 0x1e, 0xfff},
	{ 0x1030a4, 2, 0x1e, 0x924},
	{ 0x1030ac, 2, 0x1c, 0x924},
	{ 0x1030b4, 1, 0x4, 0x924},
	{ 0x1030b8, 2, 0x1c, 0xfff},
	{ 0x1030c0, 3, 0x1c, 0x924},
	{ 0x1030cc, 1, 0x1c, 0xfff},
	{ 0x1030d0, 1, 0x1c, 0x924},
	{ 0x1030d8, 2, 0x1c, 0x924},
	{ 0x1030e0, 1, 0x1c, 0xfff},
	{ 0x1030e4, 5, 0x1c, 0x924},
	{ 0x103400, 136, 0x1c, 0x1fff},
	{ 0x103800, 8, 0x1f, 0x924},
	{ 0x104000, 1, 0x1f, 0x924},
	{ 0x104004, 1, 0x1f, 0xfff},
	{ 0x104008, 4, 0x1f, 0x924},
	{ 0x104018, 1, 0x1f, 0xfff},
	{ 0x10401c, 1, 0x1f, 0x924},
	{ 0x104020, 1, 0x1f, 0xfff},
	{ 0x104024, 6, 0x1f, 0x924},
	{ 0x10403c, 1, 0x1f, 0xfff},
	{ 0x104040, 47, 0x1f, 0x924},
	{ 0x10410c, 2, 0x1f, 0x1fff},
	{ 0x104118, 1, 0x1f, 0x1fff},
	{ 0x10411c, 16, 0x1c, 0x924},
	{ 0x104200, 17, 0x1f, 0x924},
	{ 0x104400, 1, 0x1f, 0x1fff},
	{ 0x104404, 63, 0x1f, 0xfff},
	{ 0x104500, 192, 0x1f, 0xdb6},
	{ 0x104800, 1, 0x1f, 0x1fff},
	{ 0x104804, 63, 0x1f, 0xfff},
	{ 0x104900, 192, 0x1f, 0xdb6},
	{ 0x105000, 4, 0x1f, 0x1fff},
	{ 0x105010, 252, 0x1f, 0xfff},
	{ 0x105400, 768, 0x1f, 0xdb6},
	{ 0x107000, 7, 0x1c, 0x924},
	{ 0x10701c, 1, 0x18, 0x924},
	{ 0x108000, 33, 0x3, 0x924},
	{ 0x1080ac, 5, 0x2, 0x924},
	{ 0x108100, 5, 0x3, 0x924},
	{ 0x108120, 5, 0x3, 0x924},
	{ 0x108200, 74, 0x3, 0x924},
	{ 0x108400, 74, 0x3, 0x924},
	{ 0x108800, 152, 0x3, 0x924},
	{ 0x110000, 111, 0x1c, 0x924},
	{ 0x1101cc, 2, 0x1c, 0x1fff},
	{ 0x1101d8, 1, 0x1c, 0x1fff},
	{ 0x1101dc, 1, 0x18, 0x924},
	{ 0x110200, 4, 0x1c, 0x924},
	{ 0x120000, 92, 0x1f, 0x924},
	{ 0x120170, 2, 0x3, 0x924},
	{ 0x120178, 14, 0x1f, 0x924},
	{ 0x1201b0, 2, 0x1f, 0xfff},
	{ 0x1201b8, 93, 0x1f, 0x924},
	{ 0x12032c, 1, 0x1f, 0xfff},
	{ 0x120330, 15, 0x1f, 0x924},
	{ 0x12036c, 3, 0x1f, 0xfff},
	{ 0x120378, 36, 0x1f, 0x924},
	{ 0x120408, 2, 0x1f, 0xfff},
	{ 0x120410, 1, 0x1f, 0x924},
	{ 0x120414, 15, 0x1f, 0xfff},
	{ 0x120450, 10, 0x1f, 0x924},
	{ 0x120478, 2, 0x1f, 0xfff},
	{ 0x120480, 43, 0x1f, 0x924},
	{ 0x12052c, 1, 0x1f, 0xfff},
	{ 0x120530, 5, 0x1f, 0x924},
	{ 0x120544, 4, 0x3, 0x924},
	{ 0x120554, 4, 0x1f, 0x924},
	{ 0x120564, 2, 0x1f, 0xfff},
	{ 0x12057c, 2, 0x1f, 0x1fff},
	{ 0x120588, 3, 0x1f, 0x1fff},
	{ 0x120598, 1, 0x1f, 0x1fff},
	{ 0x12059c, 22, 0x1e, 0x924},
	{ 0x1205f4, 1, 0x6, 0x924},
	{ 0x1205f8, 4, 0x1c, 0x924},
	{ 0x120618, 1, 0x1c, 0x924},
	{ 0x12061c, 31, 0x1e, 0x924},
	{ 0x120698, 3, 0x1c, 0x924},
	{ 0x1206a4, 1, 0x4, 0x924},
	{ 0x1206a8, 1, 0x1c, 0x924},
	{ 0x1206b0, 38, 0x1c, 0x924},
	{ 0x120748, 1, 0x1c, 0xfff},
	{ 0x12074c, 11, 0x1c, 0x924},
	{ 0x120778, 2, 0x1c, 0xfff},
	{ 0x120780, 23, 0x1c, 0x924},
	{ 0x1207dc, 1, 0x4, 0x924},
	{ 0x1207fc, 1, 0x1c, 0x924},
	{ 0x12080c, 2, 0x1f, 0xfff},
	{ 0x120814, 1, 0x1f, 0x924},
	{ 0x120818, 1, 0x1f, 0xfff},
	{ 0x12081c, 1, 0x1f, 0x924},
	{ 0x120820, 1, 0x1f, 0xfff},
	{ 0x120824, 1, 0x1f, 0x924},
	{ 0x120828, 1, 0x1f, 0xfff},
	{ 0x12082c, 1, 0x1f, 0x924},
	{ 0x120830, 1, 0x1f, 0xfff},
	{ 0x120834, 1, 0x1f, 0x924},
	{ 0x120838, 1, 0x1f, 0xfff},
	{ 0x12083c, 1, 0x1f, 0x924},
	{ 0x120840, 1, 0x1f, 0xfff},
	{ 0x120844, 1, 0x1f, 0x924},
	{ 0x120848, 1, 0x1f, 0xfff},
	{ 0x12084c, 1, 0x1f, 0x924},
	{ 0x120850, 1, 0x1f, 0xfff},
	{ 0x120854, 1, 0x1f, 0x924},
	{ 0x120858, 1, 0x1f, 0xfff},
	{ 0x12085c, 1, 0x1f, 0x924},
	{ 0x120860, 1, 0x1f, 0xfff},
	{ 0x120864, 1, 0x1f, 0x924},
	{ 0x120868, 1, 0x1f, 0xfff},
	{ 0x12086c, 1, 0x1f, 0x924},
	{ 0x120870, 1, 0x1f, 0xfff},
	{ 0x120874, 1, 0x1f, 0x924},
	{ 0x120878, 1, 0x1f, 0xfff},
	{ 0x12087c, 1, 0x1f, 0x924},
	{ 0x120880, 1, 0x1f, 0xfff},
	{ 0x120884, 1, 0x1f, 0x924},
	{ 0x120888, 1, 0x1f, 0xfff},
	{ 0x12088c, 1, 0x1f, 0x924},
	{ 0x120890, 1, 0x1f, 0xfff},
	{ 0x120894, 1, 0x1f, 0x924},
	{ 0x120898, 1, 0x1f, 0xfff},
	{ 0x12089c, 1, 0x1f, 0x924},
	{ 0x1208a0, 1, 0x1f, 0xfff},
	{ 0x1208a4, 1, 0x1f, 0x924},
	{ 0x1208a8, 1, 0x1f, 0xfff},
	{ 0x1208ac, 1, 0x1f, 0x924},
	{ 0x1208b0, 1, 0x1f, 0xfff},
	{ 0x1208b4, 1, 0x1f, 0x924},
	{ 0x1208b8, 1, 0x1f, 0xfff},
	{ 0x1208bc, 1, 0x1f, 0x924},
	{ 0x1208c0, 1, 0x1f, 0xfff},
	{ 0x1208c4, 1, 0x1f, 0x924},
	{ 0x1208c8, 1, 0x1f, 0xfff},
	{ 0x1208cc, 1, 0x1f, 0x924},
	{ 0x1208d0, 1, 0x1f, 0xfff},
	{ 0x1208d4, 1, 0x1f, 0x924},
	{ 0x1208d8, 1, 0x1f, 0xfff},
	{ 0x1208dc, 1, 0x1f, 0x924},
	{ 0x1208e0, 1, 0x1f, 0xfff},
	{ 0x1208e4, 1, 0x1f, 0x924},
	{ 0x1208e8, 1, 0x1f, 0xfff},
	{ 0x1208ec, 1, 0x1f, 0x924},
	{ 0x1208f0, 1, 0x1f, 0xfff},
	{ 0x1208f4, 1, 0x1f, 0x924},
	{ 0x1208f8, 1, 0x1f, 0xfff},
	{ 0x1208fc, 1, 0x1f, 0x924},
	{ 0x120900, 1, 0x1f, 0xfff},
	{ 0x120904, 1, 0x1f, 0x924},
	{ 0x120908, 1, 0x1f, 0xfff},
	{ 0x12090c, 1, 0x1f, 0x924},
	{ 0x120910, 7, 0x1c, 0x924},
	{ 0x120930, 9, 0x1c, 0x924},
	{ 0x12095c, 37, 0x18, 0x924},
	{ 0x120a00, 2, 0x7, 0x924},
	{ 0x120b00, 1, 0x18, 0x924},
	{ 0x122000, 2, 0x1f, 0x924},
	{ 0x122008, 2046, 0x1, 0x924},
	{ 0x128000, 6144, 0x1e, 0x924},
	{ 0x130000, 1, 0x1c, 0x1fff},
	{ 0x130004, 11, 0x1c, 0x924},
	{ 0x130030, 1, 0x1c, 0xfff},
	{ 0x130034, 6, 0x1c, 0x924},
	{ 0x13004c, 3, 0x1c, 0xfff},
	{ 0x130058, 3, 0x1c, 0x924},
	{ 0x130064, 2, 0x1c, 0xfff},
	{ 0x13006c, 8, 0x1c, 0x924},
	{ 0x13009c, 2, 0x1c, 0x1fff},
	{ 0x1300a8, 1, 0x1c, 0x1fff},
	{ 0x130100, 12, 0x1c, 0x924},
	{ 0x130130, 1, 0x1c, 0xfff},
	{ 0x130134, 14, 0x1c, 0x924},
	{ 0x13016c, 1, 0x1c, 0xfff},
	{ 0x130170, 1, 0x1c, 0x924},
	{ 0x130180, 1, 0x1c, 0x924},
	{ 0x130200, 1, 0x1c, 0x924},
	{ 0x130280, 1, 0x1c, 0x924},
	{ 0x130300, 1, 0x1c, 0xfff},
	{ 0x130304, 4, 0x1c, 0x924},
	{ 0x130380, 1, 0x1c, 0x924},
	{ 0x130400, 1, 0x1c, 0x924},
	{ 0x130480, 1, 0x1c, 0xfff},
	{ 0x130484, 4, 0x1c, 0x924},
	{ 0x130800, 72, 0x1c, 0x924},
	{ 0x131000, 136, 0x1c, 0x924},
	{ 0x132000, 148, 0x1c, 0x924},
	{ 0x134000, 544, 0x1c, 0x924},
	{ 0x140000, 1, 0x1f, 0x924},
	{ 0x140004, 9, 0xf, 0x924},
	{ 0x140028, 8, 0x1f, 0x924},
	{ 0x140048, 5, 0xf, 0x924},
	{ 0x14005c, 2, 0xf, 0xfff},
	{ 0x140064, 3, 0xf, 0x924},
	{ 0x140070, 1, 0x1f, 0x924},
	{ 0x140074, 10, 0xf, 0x924},
	{ 0x14009c, 1, 0x1f, 0x924},
	{ 0x1400a0, 5, 0xf, 0x924},
	{ 0x1400b4, 7, 0x1f, 0x924},
	{ 0x1400d0, 2, 0xf, 0xfff},
	{ 0x1400d8, 2, 0xf, 0x924},
	{ 0x1400e0, 1, 0xf, 0xfff},
	{ 0x1400e4, 5, 0xf, 0x924},
	{ 0x1400f8, 2, 0x1f, 0x924},
	{ 0x140100, 5, 0x3, 0x924},
	{ 0x140114, 5, 0xf, 0x924},
	{ 0x140128, 7, 0x1f, 0x924},
	{ 0x140144, 9, 0xf, 0x924},
	{ 0x140168, 8, 0x1f, 0x924},
	{ 0x140188, 3, 0xf, 0x924},
	{ 0x140194, 13, 0x1f, 0x924},
	{ 0x1401d8, 2, 0x1f, 0x1fff},
	{ 0x1401e4, 1, 0x1f, 0x1fff},
	{ 0x140200, 6, 0xf, 0xfff},
	{ 0x1402e0, 2, 0xc, 0x924},
	{ 0x1402e8, 2, 0x1c, 0x924},
	{ 0x1402f0, 9, 0xc, 0x924},
	{ 0x140314, 9, 0x10, 0x924},
	{ 0x140338, 7, 0x10, 0xfff},
	{ 0x140354, 7, 0x10, 0x924},
	{ 0x140370, 7, 0x10, 0xfff},
	{ 0x14038c, 14, 0x10, 0x924},
	{ 0x1404b0, 14, 0x10, 0x924},
	{ 0x15c000, 2, 0x1e, 0x924},
	{ 0x15c008, 5, 0x2, 0x924},
	{ 0x15c020, 8, 0x1c, 0x924},
	{ 0x15c040, 1, 0xc, 0x924},
	{ 0x15c044, 2, 0x1c, 0x924},
	{ 0x15c04c, 8, 0xc, 0x924},
	{ 0x15c06c, 8, 0x1c, 0x924},
	{ 0x15c090, 13, 0x1c, 0x924},
	{ 0x15c0c8, 24, 0x1c, 0x924},
	{ 0x15c128, 2, 0xc, 0x924},
	{ 0x15c130, 1, 0x1c, 0x924},
	{ 0x15c138, 6, 0x1c, 0x924},
	{ 0x15c150, 2, 0x18, 0x924},
	{ 0x15c158, 2, 0x8, 0x924},
	{ 0x15c160, 23, 0x10, 0x924},
	{ 0x15c1bc, 6, 0x10, 0xfff},
	{ 0x15c1d4, 23, 0x10, 0x924},
	{ 0x15c230, 7, 0x10, 0xfff},
	{ 0x15c24c, 90, 0x10, 0x924},
	{ 0x160004, 6, 0x18, 0x924},
	{ 0x16003c, 1, 0x10, 0x924},
	{ 0x160040, 6, 0x18, 0x924},
	{ 0x16005c, 6, 0x18, 0x924},
	{ 0x160074, 1, 0x10, 0x924},
	{ 0x160078, 2, 0x18, 0x924},
	{ 0x160300, 8, 0x18, 0x924},
	{ 0x160330, 6, 0x18, 0x924},
	{ 0x160404, 6, 0x18, 0x924},
	{ 0x16043c, 1, 0x10, 0x924},
	{ 0x160440, 6, 0x18, 0x924},
	{ 0x16045c, 6, 0x18, 0x924},
	{ 0x160474, 1, 0x10, 0x924},
	{ 0x160478, 2, 0x18, 0x924},
	{ 0x160700, 8, 0x18, 0x924},
	{ 0x160730, 6, 0x18, 0x924},
	{ 0x161000, 7, 0x1f, 0x924},
	{ 0x16102c, 2, 0x1f, 0x1fff},
	{ 0x161038, 1, 0x1f, 0x1fff},
	{ 0x16103c, 2, 0x1c, 0x924},
	{ 0x161800, 2, 0x1f, 0x924},
	{ 0x162000, 54, 0x18, 0x924},
	{ 0x162200, 60, 0x18, 0x924},
	{ 0x162400, 54, 0x18, 0x924},
	{ 0x162600, 60, 0x18, 0x924},
	{ 0x162800, 54, 0x18, 0x924},
	{ 0x162a00, 60, 0x18, 0x924},
	{ 0x162c00, 54, 0x18, 0x924},
	{ 0x162e00, 60, 0x18, 0x924},
	{ 0x163000, 1, 0x18, 0x924},
	{ 0x163008, 1, 0x18, 0x924},
	{ 0x163010, 1, 0x18, 0x924},
	{ 0x163018, 1, 0x18, 0x924},
	{ 0x163020, 5, 0x18, 0x924},
	{ 0x163038, 3, 0x18, 0x924},
	{ 0x163048, 3, 0x18, 0x924},
	{ 0x163058, 1, 0x18, 0x924},
	{ 0x163060, 1, 0x18, 0x924},
	{ 0x163068, 1, 0x18, 0x924},
	{ 0x163070, 3, 0x18, 0x924},
	{ 0x163080, 1, 0x18, 0x924},
	{ 0x163088, 3, 0x18, 0x924},
	{ 0x163098, 1, 0x18, 0x924},
	{ 0x1630a0, 1, 0x18, 0x924},
	{ 0x1630a8, 1, 0x18, 0x924},
	{ 0x1630b0, 2, 0x10, 0x924},
	{ 0x1630c0, 1, 0x18, 0x924},
	{ 0x1630c8, 1, 0x18, 0x924},
	{ 0x1630d0, 1, 0x18, 0x924},
	{ 0x1630d8, 1, 0x18, 0x924},
	{ 0x1630e0, 2, 0x18, 0x924},
	{ 0x163110, 1, 0x18, 0x924},
	{ 0x163120, 2, 0x18, 0x924},
	{ 0x163420, 4, 0x18, 0x924},
	{ 0x163438, 2, 0x18, 0x924},
	{ 0x163488, 2, 0x18, 0x924},
	{ 0x163520, 2, 0x18, 0x924},
	{ 0x163800, 1, 0x18, 0x924},
	{ 0x163808, 1, 0x18, 0x924},
	{ 0x163810, 1, 0x18, 0x924},
	{ 0x163818, 1, 0x18, 0x924},
	{ 0x163820, 5, 0x18, 0x924},
	{ 0x163838, 3, 0x18, 0x924},
	{ 0x163848, 3, 0x18, 0x924},
	{ 0x163858, 1, 0x18, 0x924},
	{ 0x163860, 1, 0x18, 0x924},
	{ 0x163868, 1, 0x18, 0x924},
	{ 0x163870, 3, 0x18, 0x924},
	{ 0x163880, 1, 0x18, 0x924},
	{ 0x163888, 3, 0x18, 0x924},
	{ 0x163898, 1, 0x18, 0x924},
	{ 0x1638a0, 1, 0x18, 0x924},
	{ 0x1638a8, 1, 0x18, 0x924},
	{ 0x1638b0, 2, 0x10, 0x924},
	{ 0x1638c0, 1, 0x18, 0x924},
	{ 0x1638c8, 1, 0x18, 0x924},
	{ 0x1638d0, 1, 0x18, 0x924},
	{ 0x1638d8, 1, 0x18, 0x924},
	{ 0x1638e0, 2, 0x18, 0x924},
	{ 0x163910, 1, 0x18, 0x924},
	{ 0x163920, 2, 0x18, 0x924},
	{ 0x163c20, 4, 0x18, 0x924},
	{ 0x163c38, 2, 0x18, 0x924},
	{ 0x163c88, 2, 0x18, 0x924},
	{ 0x163d20, 2, 0x18, 0x924},
	{ 0x164000, 5, 0x1f, 0x924},
	{ 0x164014, 2, 0x1f, 0xfff},
	{ 0x16401c, 53, 0x1f, 0x924},
	{ 0x164100, 2, 0x1f, 0x1fff},
	{ 0x16410c, 1, 0x1f, 0x1fff},
	{ 0x164110, 2, 0x1e, 0x924},
	{ 0x164118, 15, 0x1c, 0x924},
	{ 0x164200, 1, 0x1f, 0x924},
	{ 0x164208, 1, 0x1f, 0x924},
	{ 0x164210, 1, 0x1f, 0x924},
	{ 0x164218, 1, 0x1f, 0x924},
	{ 0x164220, 1, 0x1f, 0x924},
	{ 0x164228, 1, 0x1f, 0x924},
	{ 0x164230, 1, 0x1f, 0x924},
	{ 0x164238, 1, 0x1f, 0x924},
	{ 0x164240, 1, 0x1f, 0x924},
	{ 0x164248, 1, 0x1f, 0x924},
	{ 0x164250, 1, 0x1f, 0x924},
	{ 0x164258, 1, 0x1f, 0x924},
	{ 0x164260, 1, 0x1f, 0x924},
	{ 0x164270, 2, 0x1f, 0x924},
	{ 0x164280, 2, 0x1f, 0x924},
	{ 0x164800, 2, 0x1f, 0x924},
	{ 0x165000, 2, 0x1f, 0x924},
	{ 0x166000, 164, 0x1f, 0x924},
	{ 0x1662b0, 2, 0x1f, 0x1fff},
	{ 0x1662bc, 1, 0x1f, 0x1fff},
	{ 0x1662cc, 7, 0x1c, 0x924},
	{ 0x166400, 49, 0x1f, 0x924},
	{ 0x1664c8, 32, 0x1f, 0x924},
	{ 0x166548, 1, 0x1f, 0xfff},
	{ 0x16654c, 1, 0x1f, 0x924},
	{ 0x166550, 1, 0x1f, 0xfff},
	{ 0x166554, 1, 0x1f, 0x924},
	{ 0x166558, 1, 0x1f, 0xfff},
	{ 0x16655c, 1, 0x1f, 0x924},
	{ 0x166568, 2, 0x1f, 0x924},
	{ 0x166570, 5, 0x1c, 0x924},
	{ 0x166800, 1, 0x1f, 0x924},
	{ 0x168000, 1, 0x1f, 0xfff},
	{ 0x168004, 1, 0x1f, 0x924},
	{ 0x168008, 1, 0x1f, 0xfff},
	{ 0x16800c, 1, 0x1f, 0x924},
	{ 0x168010, 1, 0x1f, 0xfff},
	{ 0x168014, 1, 0x1f, 0x924},
	{ 0x168018, 1, 0x1f, 0xfff},
	{ 0x16801c, 3, 0x1f, 0x924},
	{ 0x168028, 2, 0x1f, 0xfff},
	{ 0x168030, 10, 0x1f, 0x924},
	{ 0x168058, 9, 0x1f, 0xfff},
	{ 0x16807c, 106, 0x1f, 0x924},
	{ 0x168224, 2, 0x3, 0x924},
	{ 0x16822c, 3, 0x1f, 0x924},
	{ 0x168238, 1, 0x1f, 0xfff},
	{ 0x16823c, 25, 0x1f, 0x924},
	{ 0x1682a0, 12, 0x3, 0x924},
	{ 0x1682d0, 7, 0x1f, 0xfff},
	{ 0x1682ec, 5, 0x1f, 0x924},
	{ 0x168300, 2, 0x3, 0xfff},
	{ 0x168308, 65, 0x1f, 0xfff},
	{ 0x16840c, 1, 0x1f, 0x924},
	{ 0x168410, 2, 0x1f, 0xfff},
	{ 0x168418, 2, 0x3, 0x924},
	{ 0x168420, 6, 0x1f, 0x924},
	{ 0x168448, 2, 0x1f, 0x1fff},
	{ 0x168454, 1, 0x1f, 0x1fff},
	{ 0x168800, 19, 0x1f, 0x924},
	{ 0x168900, 1, 0x1f, 0x924},
	{ 0x168a00, 128, 0x1f, 0xfff},
	{ 0x16a000, 1536, 0x1f, 0x924},
	{ 0x16c000, 1536, 0x1f, 0x924},
	{ 0x16e000, 16, 0x2, 0x924},
	{ 0x16e040, 8, 0x1c, 0x924},
	{ 0x16e100, 1, 0x2, 0x924},
	{ 0x16e200, 2, 0x2, 0xfff},
	{ 0x16e400, 1, 0x2, 0x924},
	{ 0x16e404, 2, 0x2, 0xfff},
	{ 0x16e40c, 94, 0x2, 0x924},
	{ 0x16e584, 64, 0x2, 0xfff},
	{ 0x16e684, 2, 0x1e, 0xfff},
	{ 0x16e68c, 4, 0x2, 0xfff},
	{ 0x16e69c, 8, 0x2, 0x924},
	{ 0x16e6bc, 4, 0x1e, 0x924},
	{ 0x16e6cc, 4, 0x2, 0x924},
	{ 0x16e6e0, 2, 0x1c, 0x924},
	{ 0x16e6e8, 5, 0xc, 0x924},
	{ 0x16e6fc, 4, 0x1c, 0xfff},
	{ 0x16e70c, 1, 0x1c, 0x924},
	{ 0x16e768, 17, 0x1c, 0x924},
	{ 0x16e7ac, 12, 0x10, 0xfff},
	{ 0x170000, 24, 0x1f, 0x924},
	{ 0x170060, 4, 0x3, 0x924},
	{ 0x170070, 13, 0x1f, 0x924},
	{ 0x1700a4, 1, 0x1f, 0xfff},
	{ 0x1700a8, 1, 0x1f, 0x924},
	{ 0x1700ac, 2, 0x1f, 0xfff},
	{ 0x1700b4, 3, 0x1f, 0x924},
	{ 0x1700c0, 1, 0x1f, 0xfff},
	{ 0x1700c4, 44, 0x1f, 0x924},
	{ 0x170184, 2, 0x1f, 0x1fff},
	{ 0x170190, 1, 0x1f, 0x1fff},
	{ 0x170194, 11, 0x1c, 0x924},
	{ 0x1701c4, 1, 0x1c, 0x924},
	{ 0x1701cc, 7, 0x1c, 0x924},
	{ 0x1701e8, 1, 0x18, 0x924},
	{ 0x1701ec, 1, 0x1c, 0x924},
	{ 0x1701f4, 1, 0x1c, 0x924},
	{ 0x170200, 4, 0x1f, 0x924},
	{ 0x170214, 1, 0x1f, 0x924},
	{ 0x170218, 77, 0x1c, 0x924},
	{ 0x170400, 64, 0x1c, 0x924},
	{ 0x178000, 1, 0x1f, 0x924},
	{ 0x180000, 61, 0x1f, 0x924},
	{ 0x180114, 2, 0x1f, 0x1fff},
	{ 0x180120, 3, 0x1f, 0x1fff},
	{ 0x180130, 1, 0x1f, 0x1fff},
	{ 0x18013c, 2, 0x1e, 0x924},
	{ 0x180200, 27, 0x1f, 0x924},
	{ 0x18026c, 1, 0x1f, 0xfff},
	{ 0x180270, 12, 0x1f, 0x924},
	{ 0x1802a0, 1, 0x1f, 0xfff},
	{ 0x1802a4, 17, 0x1f, 0x924},
	{ 0x180340, 4, 0x1f, 0x924},
	{ 0x180380, 1, 0x1c, 0x924},
	{ 0x180388, 1, 0x1c, 0x924},
	{ 0x180390, 1, 0x1c, 0x924},
	{ 0x180398, 1, 0x1c, 0x924},
	{ 0x1803a0, 5, 0x1c, 0x924},
	{ 0x1803b4, 2, 0x18, 0x924},
	{ 0x181000, 4, 0x1f, 0x93c},
	{ 0x181010, 1020, 0x1f, 0x38},
	{ 0x182000, 4, 0x18, 0x924},
	{ 0x1a0000, 1, 0x1f, 0x92c},
	{ 0x1a0004, 5631, 0x1f, 0x8},
	{ 0x1a5800, 2560, 0x1e, 0x8},
	{ 0x1a8000, 1, 0x1f, 0x92c},
	{ 0x1a8004, 8191, 0x1e, 0x8},
	{ 0x1b0000, 1, 0x1f, 0x92c},
	{ 0x1b0004, 15, 0x2, 0x8},
	{ 0x1b0040, 1, 0x1e, 0x92c},
	{ 0x1b0044, 239, 0x2, 0x8},
	{ 0x1b0400, 1, 0x1f, 0x92c},
	{ 0x1b0404, 255, 0x2, 0x8},
	{ 0x1b0800, 1, 0x1f, 0x924},
	{ 0x1b0840, 1, 0x1e, 0x924},
	{ 0x1b0c00, 1, 0x1f, 0x1fff},
	{ 0x1b1000, 1, 0x1f, 0x1fff},
	{ 0x1b1040, 1, 0x1e, 0x1fff},
	{ 0x1b1400, 1, 0x1f, 0x924},
	{ 0x1b1440, 1, 0x1e, 0x924},
	{ 0x1b1480, 1, 0x1e, 0x924},
	{ 0x1b14c0, 1, 0x1e, 0x924},
	{ 0x1b1800, 128, 0x1f, 0x10},
	{ 0x1b1c00, 128, 0x1f, 0x10},
	{ 0x1b2000, 1, 0x1f, 0xdb6},
	{ 0x1b2400, 1, 0x1e, 0x92c},
	{ 0x1b2404, 5631, 0x1c, 0x8},
	{ 0x1b8000, 1, 0x1f, 0xfff},
	{ 0x1b8040, 1, 0x1f, 0xfff},
	{ 0x1b8080, 1, 0x1f, 0xfff},
	{ 0x1b80c0, 1, 0x1f, 0xfff},
	{ 0x1b8100, 1, 0x1f, 0x924},
	{ 0x1b8140, 1, 0x1f, 0x924},
	{ 0x1b8180, 1, 0x1f, 0x924},
	{ 0x1b81c0, 1, 0x1f, 0x924},
	{ 0x1b8200, 1, 0x1f, 0x924},
	{ 0x1b8240, 1, 0x1f, 0x924},
	{ 0x1b8280, 1, 0x1f, 0x924},
	{ 0x1b82c0, 1, 0x1f, 0x924},
	{ 0x1b8300, 1, 0x1f, 0x924},
	{ 0x1b8340, 1, 0x1f, 0x924},
	{ 0x1b8380, 1, 0x1f, 0x924},
	{ 0x1b83c0, 1, 0x1f, 0x924},
	{ 0x1b8400, 1, 0x1f, 0x924},
	{ 0x1b8440, 1, 0x1f, 0x924},
	{ 0x1b8480, 1, 0x1f, 0x924},
	{ 0x1b84c0, 1, 0x1f, 0x924},
	{ 0x1b8500, 1, 0x1f, 0x924},
	{ 0x1b8540, 1, 0x1f, 0x924},
	{ 0x1b8580, 1, 0x1f, 0x924},
	{ 0x1b85c0, 19, 0x1c, 0x924},
	{ 0x1b8800, 1, 0x1f, 0x924},
	{ 0x1b8840, 1, 0x1f, 0x924},
	{ 0x1b8880, 1, 0x1f, 0x924},
	{ 0x1b88c0, 1, 0x1f, 0x924},
	{ 0x1b8900, 1, 0x1f, 0x924},
	{ 0x1b8940, 1, 0x1f, 0x924},
	{ 0x1b8980, 1, 0x1f, 0x924},
	{ 0x1b89c0, 1, 0x1f, 0x924},
	{ 0x1b8a00, 1, 0x1f, 0x934},
	{ 0x1b8a40, 1, 0x1f, 0x924},
	{ 0x1b8a80, 1, 0x1f, 0x492},
	{ 0x1b8ac0, 1, 0x1f, 0x924},
	{ 0x1b8b00, 1, 0x1f, 0x924},
	{ 0x1b8b40, 1, 0x1f, 0x924},
	{ 0x1b8b80, 1, 0x1f, 0x924},
	{ 0x1b8bc0, 1, 0x1f, 0x924},
	{ 0x1b8c00, 1, 0x1f, 0x924},
	{ 0x1b8c40, 1, 0x1f, 0x924},
	{ 0x1b8c80, 1, 0x1f, 0x924},
	{ 0x1b8cc0, 1, 0x1f, 0x924},
	{ 0x1b8cc4, 1, 0x1c, 0x924},
	{ 0x1b8d00, 1, 0x1f, 0x924},
	{ 0x1b8d40, 1, 0x1f, 0x924},
	{ 0x1b8d80, 1, 0x1f, 0x924},
	{ 0x1b8dc0, 1, 0x1f, 0x924},
	{ 0x1b8e00, 1, 0x1f, 0x924},
	{ 0x1b8e40, 1, 0x1f, 0x924},
	{ 0x1b8e80, 1, 0x1f, 0x924},
	{ 0x1b8e84, 1, 0x1c, 0x924},
	{ 0x1b8ec0, 1, 0x1e, 0x924},
	{ 0x1b8f00, 1, 0x1e, 0x924},
	{ 0x1b8f40, 1, 0x1e, 0x924},
	{ 0x1b8f80, 1, 0x1e, 0x924},
	{ 0x1b8fc0, 1, 0x1e, 0x924},
	{ 0x1b8fd4, 5, 0x1c, 0x924},
	{ 0x1b8fe8, 2, 0x18, 0x924},
	{ 0x1b9000, 1, 0x1c, 0x924},
	{ 0x1b9040, 3, 0x1c, 0x924},
	{ 0x1b905c, 1, 0x18, 0x924},
	{ 0x1b9064, 1, 0x10, 0x924},
	{ 0x1b9080, 10, 0x10, 0x924},
	{ 0x1c0000, 2, 0x1f, 0x924},
	{ 0x200000, 65, 0x1f, 0x924},
	{ 0x200124, 2, 0x1f, 0x1fff},
	{ 0x200130, 3, 0x1f, 0x1fff},
	{ 0x200140, 1, 0x1f, 0x1fff},
	{ 0x20014c, 2, 0x1e, 0x924},
	{ 0x200200, 27, 0x1f, 0x924},
	{ 0x20026c, 1, 0x1f, 0xfff},
	{ 0x200270, 12, 0x1f, 0x924},
	{ 0x2002a0, 1, 0x1f, 0xfff},
	{ 0x2002a4, 17, 0x1f, 0x924},
	{ 0x200340, 4, 0x1f, 0x924},
	{ 0x200380, 1, 0x1c, 0x924},
	{ 0x200388, 1, 0x1c, 0x924},
	{ 0x200390, 1, 0x1c, 0x924},
	{ 0x200398, 1, 0x1c, 0x924},
	{ 0x2003a0, 1, 0x1c, 0x924},
	{ 0x2003a8, 2, 0x1c, 0x924},
	{ 0x202000, 4, 0x1f, 0x1927},
	{ 0x202010, 2044, 0x1f, 0x1007},
	{ 0x204000, 4, 0x18, 0x924},
	{ 0x220000, 1, 0x1f, 0x925},
	{ 0x220004, 5631, 0x1f, 0x1},
	{ 0x225800, 2560, 0x1e, 0x1},
	{ 0x228000, 1, 0x1f, 0x925},
	{ 0x228004, 8191, 0x1e, 0x1},
	{ 0x230000, 1, 0x1f, 0x925},
	{ 0x230004, 15, 0x2, 0x1},
	{ 0x230040, 1, 0x1e, 0x925},
	{ 0x230044, 239, 0x2, 0x1},
	{ 0x230400, 1, 0x1f, 0x925},
	{ 0x230404, 255, 0x2, 0x1},
	{ 0x230800, 1, 0x1f, 0x924},
	{ 0x230840, 1, 0x1e, 0x924},
	{ 0x230c00, 1, 0x1f, 0x924},
	{ 0x231000, 1, 0x1f, 0x924},
	{ 0x231040, 1, 0x1e, 0x924},
	{ 0x231400, 1, 0x1f, 0x924},
	{ 0x231440, 1, 0x1e, 0x924},
	{ 0x231480, 1, 0x1e, 0x924},
	{ 0x2314c0, 1, 0x1e, 0x924},
	{ 0x231800, 128, 0x1f, 0x2},
	{ 0x231c00, 128, 0x1f, 0x2},
	{ 0x232000, 1, 0x1f, 0xdb6},
	{ 0x232400, 1, 0x1e, 0x925},
	{ 0x232404, 5631, 0x1c, 0x1},
	{ 0x238000, 1, 0x1f, 0xfff},
	{ 0x238040, 1, 0x1f, 0xfff},
	{ 0x238080, 1, 0x1f, 0xfff},
	{ 0x2380c0, 1, 0x1f, 0xfff},
	{ 0x238100, 1, 0x1f, 0x924},
	{ 0x238140, 1, 0x1f, 0x924},
	{ 0x238180, 1, 0x1f, 0x924},
	{ 0x2381c0, 1, 0x1f, 0x924},
	{ 0x238200, 1, 0x1f, 0x924},
	{ 0x238240, 1, 0x1f, 0x924},
	{ 0x238280, 1, 0x1f, 0x924},
	{ 0x2382c0, 1, 0x1f, 0x924},
	{ 0x238300, 1, 0x1f, 0x924},
	{ 0x238340, 1, 0x1f, 0x924},
	{ 0x238380, 1, 0x1f, 0x924},
	{ 0x2383c0, 1, 0x1f, 0x924},
	{ 0x238400, 1, 0x1f, 0x924},
	{ 0x238440, 1, 0x1f, 0x924},
	{ 0x238480, 1, 0x1f, 0x924},
	{ 0x2384c0, 1, 0x1f, 0x924},
	{ 0x238500, 1, 0x1f, 0x924},
	{ 0x238540, 1, 0x1f, 0x924},
	{ 0x238580, 1, 0x1f, 0x924},
	{ 0x2385c0, 19, 0x1c, 0x924},
	{ 0x238800, 1, 0x1f, 0x924},
	{ 0x238840, 1, 0x1f, 0x924},
	{ 0x238880, 1, 0x1f, 0x924},
	{ 0x2388c0, 1, 0x1f, 0x924},
	{ 0x238900, 1, 0x1f, 0x924},
	{ 0x238940, 1, 0x1f, 0x924},
	{ 0x238980, 1, 0x1f, 0x924},
	{ 0x2389c0, 1, 0x1f, 0x924},
	{ 0x238a00, 1, 0x1f, 0x926},
	{ 0x238a40, 1, 0x1f, 0x924},
	{ 0x238a80, 1, 0x1f, 0x492},
	{ 0x238ac0, 1, 0x1f, 0x924},
	{ 0x238b00, 1, 0x1f, 0x924},
	{ 0x238b40, 1, 0x1f, 0x924},
	{ 0x238b80, 1, 0x1f, 0x924},
	{ 0x238bc0, 1, 0x1f, 0x924},
	{ 0x238c00, 1, 0x1f, 0x924},
	{ 0x238c40, 1, 0x1f, 0x924},
	{ 0x238c80, 1, 0x1f, 0x924},
	{ 0x238cc0, 1, 0x1f, 0x924},
	{ 0x238cc4, 1, 0x1c, 0x924},
	{ 0x238d00, 1, 0x1f, 0x924},
	{ 0x238d40, 1, 0x1f, 0x924},
	{ 0x238d80, 1, 0x1f, 0x924},
	{ 0x238dc0, 1, 0x1f, 0x924},
	{ 0x238e00, 1, 0x1f, 0x924},
	{ 0x238e40, 1, 0x1f, 0x924},
	{ 0x238e80, 1, 0x1f, 0x924},
	{ 0x238e84, 1, 0x1c, 0x924},
	{ 0x238ec0, 1, 0x1e, 0x924},
	{ 0x238f00, 1, 0x1e, 0x924},
	{ 0x238f40, 1, 0x1e, 0x924},
	{ 0x238f80, 1, 0x1e, 0x924},
	{ 0x238fc0, 1, 0x1e, 0x924},
	{ 0x238fd4, 5, 0x1c, 0x924},
	{ 0x238fe8, 2, 0x18, 0x924},
	{ 0x239000, 1, 0x1c, 0x924},
	{ 0x239040, 3, 0x1c, 0x924},
	{ 0x23905c, 1, 0x18, 0x924},
	{ 0x239064, 1, 0x10, 0x924},
	{ 0x239080, 10, 0x10, 0x924},
	{ 0x240000, 2, 0x1f, 0x924},
	{ 0x280000, 65, 0x1f, 0x924},
	{ 0x280124, 2, 0x1f, 0x1fff},
	{ 0x280130, 3, 0x1f, 0x1fff},
	{ 0x280140, 1, 0x1f, 0x1fff},
	{ 0x28014c, 2, 0x1e, 0x924},
	{ 0x280200, 27, 0x1f, 0x924},
	{ 0x28026c, 1, 0x1f, 0xfff},
	{ 0x280270, 12, 0x1f, 0x924},
	{ 0x2802a0, 1, 0x1f, 0xfff},
	{ 0x2802a4, 17, 0x1f, 0x924},
	{ 0x280340, 4, 0x1f, 0x924},
	{ 0x280380, 1, 0x1c, 0x924},
	{ 0x280388, 1, 0x1c, 0x924},
	{ 0x280390, 1, 0x1c, 0x924},
	{ 0x280398, 1, 0x1c, 0x924},
	{ 0x2803a0, 1, 0x1c, 0x924},
	{ 0x2803a8, 2, 0x1c, 0x924},
	{ 0x282000, 4, 0x1f, 0x9e4},
	{ 0x282010, 2044, 0x1f, 0x1c0},
	{ 0x284000, 4, 0x18, 0x924},
	{ 0x2a0000, 1, 0x1f, 0x964},
	{ 0x2a0004, 5631, 0x1f, 0x40},
	{ 0x2a5800, 2560, 0x1e, 0x40},
	{ 0x2a8000, 1, 0x1f, 0x964},
	{ 0x2a8004, 8191, 0x1e, 0x40},
	{ 0x2b0000, 1, 0x1f, 0x964},
	{ 0x2b0004, 15, 0x2, 0x40},
	{ 0x2b0040, 1, 0x1e, 0x964},
	{ 0x2b0044, 239, 0x2, 0x40},
	{ 0x2b0400, 1, 0x1f, 0x964},
	{ 0x2b0404, 255, 0x2, 0x40},
	{ 0x2b0800, 1, 0x1f, 0x924},
	{ 0x2b0840, 1, 0x1e, 0x924},
	{ 0x2b0c00, 1, 0x1f, 0x924},
	{ 0x2b1000, 1, 0x1f, 0x924},
	{ 0x2b1040, 1, 0x1e, 0x924},
	{ 0x2b1400, 1, 0x1f, 0x924},
	{ 0x2b1440, 1, 0x1e, 0x924},
	{ 0x2b1480, 1, 0x1e, 0x924},
	{ 0x2b14c0, 1, 0x1e, 0x924},
	{ 0x2b1800, 128, 0x1f, 0x80},
	{ 0x2b1c00, 128, 0x1f, 0x80},
	{ 0x2b2000, 1, 0x1f, 0xdb6},
	{ 0x2b2400, 1, 0x1e, 0x964},
	{ 0x2b2404, 5631, 0x1c, 0x40},
	{ 0x2b8000, 1, 0x1f, 0xfff},
	{ 0x2b8040, 1, 0x1f, 0xfff},
	{ 0x2b8080, 1, 0x1f, 0xfff},
	{ 0x2b80c0, 1, 0x1f, 0x924},
	{ 0x2b8100, 1, 0x1f, 0x924},
	{ 0x2b8140, 1, 0x1f, 0x924},
	{ 0x2b8180, 1, 0x1f, 0x924},
	{ 0x2b81c0, 1, 0x1f, 0x924},
	{ 0x2b8200, 1, 0x1f, 0x924},
	{ 0x2b8240, 1, 0x1f, 0x924},
	{ 0x2b8280, 1, 0x1f, 0x924},
	{ 0x2b82c0, 1, 0x1f, 0x924},
	{ 0x2b8300, 1, 0x1f, 0x924},
	{ 0x2b8340, 1, 0x1f, 0x924},
	{ 0x2b8380, 1, 0x1f, 0x924},
	{ 0x2b83c0, 1, 0x1f, 0x924},
	{ 0x2b8400, 1, 0x1f, 0x924},
	{ 0x2b8440, 1, 0x1f, 0x924},
	{ 0x2b8480, 1, 0x1f, 0x924},
	{ 0x2b84c0, 1, 0x1f, 0x924},
	{ 0x2b8500, 1, 0x1f, 0x924},
	{ 0x2b8540, 1, 0x1f, 0x924},
	{ 0x2b8580, 1, 0x1f, 0x924},
	{ 0x2b85c0, 19, 0x1c, 0x924},
	{ 0x2b8800, 1, 0x1f, 0x924},
	{ 0x2b8840, 1, 0x1f, 0x924},
	{ 0x2b8880, 1, 0x1f, 0x924},
	{ 0x2b88c0, 1, 0x1f, 0x924},
	{ 0x2b8900, 1, 0x1f, 0x924},
	{ 0x2b8940, 1, 0x1f, 0x924},
	{ 0x2b8980, 1, 0x1f, 0x924},
	{ 0x2b89c0, 1, 0x1f, 0x924},
	{ 0x2b8a00, 1, 0x1f, 0x9a4},
	{ 0x2b8a40, 1, 0x1f, 0x924},
	{ 0x2b8a80, 1, 0x1f, 0x492},
	{ 0x2b8ac0, 1, 0x1f, 0x924},
	{ 0x2b8b00, 1, 0x1f, 0x924},
	{ 0x2b8b40, 1, 0x1f, 0x924},
	{ 0x2b8b80, 1, 0x1f, 0x924},
	{ 0x2b8bc0, 1, 0x1f, 0x924},
	{ 0x2b8c00, 1, 0x1f, 0x924},
	{ 0x2b8c40, 1, 0x1f, 0x924},
	{ 0x2b8c80, 1, 0x1f, 0x924},
	{ 0x2b8cc0, 1, 0x1f, 0x924},
	{ 0x2b8cc4, 1, 0x1c, 0x924},
	{ 0x2b8d00, 1, 0x1f, 0x924},
	{ 0x2b8d40, 1, 0x1f, 0x924},
	{ 0x2b8d80, 1, 0x1f, 0x924},
	{ 0x2b8dc0, 1, 0x1f, 0x924},
	{ 0x2b8e00, 1, 0x1f, 0x924},
	{ 0x2b8e40, 1, 0x1f, 0x924},
	{ 0x2b8e80, 1, 0x1f, 0x924},
	{ 0x2b8e84, 1, 0x1c, 0x924},
	{ 0x2b8ec0, 1, 0x1e, 0x924},
	{ 0x2b8f00, 1, 0x1e, 0x924},
	{ 0x2b8f40, 1, 0x1e, 0x924},
	{ 0x2b8f80, 1, 0x1e, 0x924},
	{ 0x2b8fc0, 1, 0x1e, 0x924},
	{ 0x2b8fd4, 5, 0x1c, 0x924},
	{ 0x2b8fe8, 2, 0x18, 0x924},
	{ 0x2b9000, 1, 0x1c, 0x924},
	{ 0x2b9040, 3, 0x1c, 0x924},
	{ 0x2b905c, 1, 0x18, 0x924},
	{ 0x2b9064, 1, 0x10, 0x924},
	{ 0x2b9080, 10, 0x10, 0x924},
	{ 0x2c0000, 2, 0x1f, 0x1fff},
	{ 0x300000, 65, 0x1f, 0x924},
	{ 0x300124, 2, 0x1f, 0x1fff},
	{ 0x300130, 3, 0x1f, 0x1fff},
	{ 0x300140, 1, 0x1f, 0x1fff},
	{ 0x30014c, 2, 0x1e, 0x924},
	{ 0x300200, 27, 0x1f, 0x924},
	{ 0x30026c, 1, 0x1f, 0xfff},
	{ 0x300270, 12, 0x1f, 0x924},
	{ 0x3002a0, 1, 0x1f, 0xfff},
	{ 0x3002a4, 17, 0x1f, 0x924},
	{ 0x300340, 4, 0x1f, 0x924},
	{ 0x300380, 1, 0x1c, 0x924},
	{ 0x300388, 1, 0x1c, 0x924},
	{ 0x300390, 1, 0x1c, 0x924},
	{ 0x300398, 1, 0x1c, 0x924},
	{ 0x3003a0, 1, 0x1c, 0x924},
	{ 0x3003a8, 2, 0x1c, 0x924},
	{ 0x302000, 4, 0x1f, 0xf24},
	{ 0x302010, 2044, 0x1f, 0xe00},
	{ 0x304000, 4, 0x18, 0x924},
	{ 0x320000, 1, 0x1f, 0xb24},
	{ 0x320004, 5631, 0x1f, 0x200},
	{ 0x325800, 2560, 0x1e, 0x200},
	{ 0x328000, 1, 0x1f, 0xb24},
	{ 0x328004, 8191, 0x1e, 0x200},
	{ 0x330000, 1, 0x1f, 0xb24},
	{ 0x330004, 15, 0x2, 0x200},
	{ 0x330040, 1, 0x1e, 0xb24},
	{ 0x330044, 239, 0x2, 0x200},
	{ 0x330400, 1, 0x1f, 0xb24},
	{ 0x330404, 255, 0x2, 0x200},
	{ 0x330800, 1, 0x1f, 0x924},
	{ 0x330840, 1, 0x1e, 0x924},
	{ 0x330c00, 1, 0x1f, 0x924},
	{ 0x331000, 1, 0x1f, 0x924},
	{ 0x331040, 1, 0x1e, 0x924},
	{ 0x331400, 1, 0x1f, 0x924},
	{ 0x331440, 1, 0x1e, 0x924},
	{ 0x331480, 1, 0x1e, 0x924},
	{ 0x3314c0, 1, 0x1e, 0x924},
	{ 0x331800, 128, 0x1f, 0x400},
	{ 0x331c00, 128, 0x1f, 0x400},
	{ 0x332000, 1, 0x1f, 0xdb6},
	{ 0x332400, 1, 0x1e, 0xb24},
	{ 0x332404, 5631, 0x1c, 0x200},
	{ 0x338000, 1, 0x1f, 0xfff},
	{ 0x338040, 1, 0x1f, 0xfff},
	{ 0x338080, 1, 0x1f, 0xfff},
	{ 0x3380c0, 1, 0x1f, 0xfff},
	{ 0x338100, 1, 0x1f, 0x924},
	{ 0x338140, 1, 0x1f, 0x924},
	{ 0x338180, 1, 0x1f, 0x924},
	{ 0x3381c0, 1, 0x1f, 0x924},
	{ 0x338200, 1, 0x1f, 0x924},
	{ 0x338240, 1, 0x1f, 0x924},
	{ 0x338280, 1, 0x1f, 0x924},
	{ 0x3382c0, 1, 0x1f, 0x924},
	{ 0x338300, 1, 0x1f, 0x924},
	{ 0x338340, 1, 0x1f, 0x924},
	{ 0x338380, 1, 0x1f, 0x924},
	{ 0x3383c0, 1, 0x1f, 0x924},
	{ 0x338400, 1, 0x1f, 0x924},
	{ 0x338440, 1, 0x1f, 0x924},
	{ 0x338480, 1, 0x1f, 0x924},
	{ 0x3384c0, 1, 0x1f, 0x924},
	{ 0x338500, 1, 0x1f, 0x924},
	{ 0x338540, 1, 0x1f, 0x924},
	{ 0x338580, 1, 0x1f, 0x924},
	{ 0x3385c0, 19, 0x1c, 0x924},
	{ 0x338800, 1, 0x1f, 0x924},
	{ 0x338840, 1, 0x1f, 0x924},
	{ 0x338880, 1, 0x1f, 0x924},
	{ 0x3388c0, 1, 0x1f, 0x924},
	{ 0x338900, 1, 0x1f, 0x924},
	{ 0x338940, 1, 0x1f, 0x924},
	{ 0x338980, 1, 0x1f, 0x924},
	{ 0x3389c0, 1, 0x1f, 0x924},
	{ 0x338a00, 1, 0x1f, 0xd24},
	{ 0x338a40, 1, 0x1f, 0x924},
	{ 0x338a80, 1, 0x1f, 0x492},
	{ 0x338ac0, 1, 0x1f, 0x924},
	{ 0x338b00, 1, 0x1f, 0x924},
	{ 0x338b40, 1, 0x1f, 0x924},
	{ 0x338b80, 1, 0x1f, 0x924},
	{ 0x338bc0, 1, 0x1f, 0x924},
	{ 0x338c00, 1, 0x1f, 0x924},
	{ 0x338c40, 1, 0x1f, 0x924},
	{ 0x338c80, 1, 0x1f, 0x924},
	{ 0x338cc0, 1, 0x1f, 0x924},
	{ 0x338cc4, 1, 0x1c, 0x924},
	{ 0x338d00, 1, 0x1f, 0x924},
	{ 0x338d40, 1, 0x1f, 0x924},
	{ 0x338d80, 1, 0x1f, 0x924},
	{ 0x338dc0, 1, 0x1f, 0x924},
	{ 0x338e00, 1, 0x1f, 0x924},
	{ 0x338e40, 1, 0x1f, 0x924},
	{ 0x338e80, 1, 0x1f, 0x924},
	{ 0x338e84, 1, 0x1c, 0x924},
	{ 0x338ec0, 1, 0x1e, 0x924},
	{ 0x338f00, 1, 0x1e, 0x924},
	{ 0x338f40, 1, 0x1e, 0x924},
	{ 0x338f80, 1, 0x1e, 0x924},
	{ 0x338fc0, 1, 0x1e, 0x924},
	{ 0x338fd4, 5, 0x1c, 0x924},
	{ 0x338fe8, 2, 0x18, 0x924},
	{ 0x339000, 1, 0x1c, 0x924},
	{ 0x339040, 3, 0x1c, 0x924},
	{ 0x33905c, 1, 0x18, 0x924},
	{ 0x339064, 1, 0x10, 0x924},
	{ 0x339080, 10, 0x10, 0x924},
	{ 0x340000, 2, 0x1f, 0x924},
	{ 0x3a0000, 40960, 0x1c, 0x1000}
};

#define REGS_COUNT ARRAY_SIZE(reg_addrs)

static const struct reg_addr idle_reg_addrs[] = {
	{ 0x2104, 1, 0x1f, 0xfff},
	{ 0x2110, 2, 0x1f, 0xfff},
	{ 0x211c, 8, 0x1f, 0xfff},
	{ 0x2814, 1, 0x1f, 0xfff},
	{ 0x281c, 2, 0x1f, 0xfff},
	{ 0x2854, 1, 0x1f, 0xfff},
	{ 0x285c, 1, 0x1f, 0xfff},
	{ 0x3040, 1, 0x1f, 0xfff},
	{ 0x9010, 7, 0x1c, 0xfff},
	{ 0x9030, 1, 0x1c, 0xfff},
	{ 0x9068, 16, 0x1c, 0xfff},
	{ 0x9230, 2, 0x1c, 0xfff},
	{ 0x9244, 1, 0x1c, 0xfff},
	{ 0x9298, 1, 0x1c, 0xfff},
	{ 0x92a8, 1, 0x1c, 0x1fff},
	{ 0xa38c, 1, 0x1f, 0x1fff},
	{ 0xa3c4, 1, 0x1e, 0xfff},
	{ 0xa404, 1, 0x1f, 0xfff},
	{ 0xa408, 2, 0x1f, 0x1fff},
	{ 0xa42c, 12, 0x1f, 0xfff},
	{ 0xa580, 1, 0x1f, 0x1fff},
	{ 0xa590, 1, 0x1f, 0x1fff},
	{ 0xa600, 5, 0x1e, 0xfff},
	{ 0xa618, 1, 0x1e, 0xfff},
	{ 0xa714, 1, 0x1c, 0xfff},
	{ 0xa720, 1, 0x1c, 0xfff},
	{ 0xa750, 1, 0x1c, 0xfff},
	{ 0xc09c, 1, 0x3, 0xfff},
	{ 0x103b0, 1, 0x1f, 0xfff},
	{ 0x103c0, 1, 0x1f, 0xfff},
	{ 0x103d0, 1, 0x3, 0x1fff},
	{ 0x10418, 1, 0x1f, 0xfff},
	{ 0x10420, 1, 0x1f, 0xfff},
	{ 0x10428, 1, 0x1f, 0xfff},
	{ 0x10460, 1, 0x1f, 0xfff},
	{ 0x10474, 1, 0x1f, 0xfff},
	{ 0x104e0, 1, 0x1f, 0xfff},
	{ 0x104ec, 1, 0x1f, 0xfff},
	{ 0x104f8, 1, 0x1f, 0xfff},
	{ 0x10508, 1, 0x1f, 0xfff},
	{ 0x10530, 1, 0x1f, 0xfff},
	{ 0x10538, 1, 0x1f, 0xfff},
	{ 0x10548, 1, 0x1f, 0xfff},
	{ 0x10558, 1, 0x1f, 0xfff},
	{ 0x182a8, 1, 0x1c, 0xfff},
	{ 0x182b8, 1, 0x1c, 0xfff},
	{ 0x18308, 1, 0x1c, 0xfff},
	{ 0x18318, 1, 0x1c, 0xfff},
	{ 0x18338, 1, 0x1c, 0xfff},
	{ 0x18348, 1, 0x1c, 0xfff},
	{ 0x183bc, 1, 0x1c, 0x1fff},
	{ 0x183cc, 1, 0x1c, 0x1fff},
	{ 0x18570, 1, 0x18, 0xfff},
	{ 0x18578, 1, 0x18, 0xfff},
	{ 0x1858c, 1, 0x18, 0xfff},
	{ 0x18594, 1, 0x18, 0xfff},
	{ 0x1862c, 4, 0x10, 0xfff},
	{ 0x2021c, 11, 0x1f, 0xfff},
	{ 0x202a8, 1, 0x1f, 0xfff},
	{ 0x202b8, 1, 0x1f, 0x1fff},
	{ 0x20404, 1, 0x1f, 0xfff},
	{ 0x2040c, 2, 0x1f, 0xfff},
	{ 0x2041c, 2, 0x1f, 0xfff},
	{ 0x40154, 14, 0x1f, 0xfff},
	{ 0x40198, 1, 0x1f, 0x1fff},
	{ 0x404ac, 1, 0x1f, 0xfff},
	{ 0x404bc, 1, 0x1f, 0x1fff},
	{ 0x42290, 1, 0x1f, 0xfff},
	{ 0x422a0, 1, 0x1f, 0xfff},
	{ 0x422b0, 1, 0x1f, 0x1fff},
	{ 0x42548, 1, 0x1f, 0xfff},
	{ 0x42550, 1, 0x1f, 0xfff},
	{ 0x42558, 1, 0x1f, 0xfff},
	{ 0x50160, 8, 0x1f, 0xfff},
	{ 0x501d0, 1, 0x1f, 0xfff},
	{ 0x501e0, 1, 0x1f, 0x1fff},
	{ 0x50204, 1, 0x1f, 0xfff},
	{ 0x5020c, 2, 0x1f, 0xfff},
	{ 0x5021c, 1, 0x1f, 0xfff},
	{ 0x60090, 1, 0x1f, 0xfff},
	{ 0x6011c, 1, 0x1f, 0xfff},
	{ 0x6012c, 1, 0x1f, 0x1fff},
	{ 0xc101c, 1, 0x1f, 0xfff},
	{ 0xc102c, 1, 0x1f, 0x1fff},
	{ 0xc2290, 1, 0x1f, 0xfff},
	{ 0xc22a0, 1, 0x1f, 0xfff},
	{ 0xc22b0, 1, 0x1f, 0x1fff},
	{ 0xc2548, 1, 0x1f, 0xfff},
	{ 0xc2550, 1, 0x1f, 0xfff},
	{ 0xc2558, 1, 0x1f, 0xfff},
	{ 0xc4294, 1, 0x1f, 0xfff},
	{ 0xc42a4, 1, 0x1f, 0xfff},
	{ 0xc42b4, 1, 0x1f, 0x1fff},
	{ 0xc4550, 1, 0x1f, 0xfff},
	{ 0xc4558, 1, 0x1f, 0xfff},
	{ 0xc4560, 1, 0x1f, 0xfff},
	{ 0xd016c, 8, 0x1f, 0xfff},
	{ 0xd01d8, 1, 0x1f, 0xfff},
	{ 0xd01e8, 1, 0x1f, 0x1fff},
	{ 0xd0204, 1, 0x1f, 0xfff},
	{ 0xd020c, 3, 0x1f, 0xfff},
	{ 0xe0154, 8, 0x1f, 0xfff},
	{ 0xe01c8, 1, 0x1f, 0xfff},
	{ 0xe01d8, 1, 0x1f, 0x1fff},
	{ 0xe0204, 1, 0x1f, 0xfff},
	{ 0xe020c, 2, 0x1f, 0xfff},
	{ 0xe021c, 2, 0x1f, 0xfff},
	{ 0x101014, 1, 0x1f, 0xfff},
	{ 0x101030, 1, 0x1f, 0xfff},
	{ 0x101040, 1, 0x1f, 0x1fff},
	{ 0x102058, 1, 0x1f, 0x1fff},
	{ 0x102080, 16, 0x1f, 0xfff},
	{ 0x103004, 2, 0x1f, 0xfff},
	{ 0x103068, 1, 0x1f, 0xfff},
	{ 0x103078, 1, 0x1f, 0xfff},
	{ 0x103088, 1, 0x1f, 0x1fff},
	{ 0x10309c, 2, 0x1e, 0xfff},
	{ 0x1030b8, 2, 0x1c, 0xfff},
	{ 0x1030cc, 1, 0x1c, 0xfff},
	{ 0x1030e0, 1, 0x1c, 0xfff},
	{ 0x104004, 1, 0x1f, 0xfff},
	{ 0x104018, 1, 0x1f, 0xfff},
	{ 0x104020, 1, 0x1f, 0xfff},
	{ 0x10403c, 1, 0x1f, 0xfff},
	{ 0x1040fc, 1, 0x1f, 0xfff},
	{ 0x10410c, 1, 0x1f, 0x1fff},
	{ 0x104400, 1, 0x1f, 0x1fff},
	{ 0x104404, 63, 0x1f, 0xfff},
	{ 0x104800, 1, 0x1f, 0x1fff},
	{ 0x104804, 63, 0x1f, 0xfff},
	{ 0x105000, 4, 0x1f, 0x1fff},
	{ 0x105010, 252, 0x1f, 0xfff},
	{ 0x108094, 1, 0x3, 0xfff},
	{ 0x1201b0, 2, 0x1f, 0xfff},
	{ 0x12032c, 1, 0x1f, 0xfff},
	{ 0x12036c, 3, 0x1f, 0xfff},
	{ 0x120408, 2, 0x1f, 0xfff},
	{ 0x120414, 15, 0x1f, 0xfff},
	{ 0x120478, 2, 0x1f, 0xfff},
	{ 0x12052c, 1, 0x1f, 0xfff},
	{ 0x120564, 3, 0x1f, 0xfff},
	{ 0x12057c, 1, 0x1f, 0x1fff},
	{ 0x12058c, 1, 0x1f, 0x1fff},
	{ 0x120608, 1, 0x1e, 0xfff},
	{ 0x120748, 1, 0x1c, 0xfff},
	{ 0x120778, 2, 0x1c, 0xfff},
	{ 0x120808, 3, 0x1f, 0xfff},
	{ 0x120818, 1, 0x1f, 0xfff},
	{ 0x120820, 1, 0x1f, 0xfff},
	{ 0x120828, 1, 0x1f, 0xfff},
	{ 0x120830, 1, 0x1f, 0xfff},
	{ 0x120838, 1, 0x1f, 0xfff},
	{ 0x120840, 1, 0x1f, 0xfff},
	{ 0x120848, 1, 0x1f, 0xfff},
	{ 0x120850, 1, 0x1f, 0xfff},
	{ 0x120858, 1, 0x1f, 0xfff},
	{ 0x120860, 1, 0x1f, 0xfff},
	{ 0x120868, 1, 0x1f, 0xfff},
	{ 0x120870, 1, 0x1f, 0xfff},
	{ 0x120878, 1, 0x1f, 0xfff},
	{ 0x120880, 1, 0x1f, 0xfff},
	{ 0x120888, 1, 0x1f, 0xfff},
	{ 0x120890, 1, 0x1f, 0xfff},
	{ 0x120898, 1, 0x1f, 0xfff},
	{ 0x1208a0, 1, 0x1f, 0xfff},
	{ 0x1208a8, 1, 0x1f, 0xfff},
	{ 0x1208b0, 1, 0x1f, 0xfff},
	{ 0x1208b8, 1, 0x1f, 0xfff},
	{ 0x1208c0, 1, 0x1f, 0xfff},
	{ 0x1208c8, 1, 0x1f, 0xfff},
	{ 0x1208d0, 1, 0x1f, 0xfff},
	{ 0x1208d8, 1, 0x1f, 0xfff},
	{ 0x1208e0, 1, 0x1f, 0xfff},
	{ 0x1208e8, 1, 0x1f, 0xfff},
	{ 0x1208f0, 1, 0x1f, 0xfff},
	{ 0x1208f8, 1, 0x1f, 0xfff},
	{ 0x120900, 1, 0x1f, 0xfff},
	{ 0x120908, 1, 0x1f, 0xfff},
	{ 0x130030, 1, 0x1c, 0xfff},
	{ 0x13004c, 3, 0x1c, 0xfff},
	{ 0x130064, 2, 0x1c, 0xfff},
	{ 0x13009c, 1, 0x1c, 0x1fff},
	{ 0x130130, 1, 0x1c, 0xfff},
	{ 0x13016c, 1, 0x1c, 0xfff},
	{ 0x130300, 1, 0x1c, 0xfff},
	{ 0x130480, 1, 0x1c, 0xfff},
	{ 0x14005c, 2, 0xf, 0xfff},
	{ 0x1400d0, 2, 0xf, 0xfff},
	{ 0x1400e0, 1, 0xf, 0xfff},
	{ 0x1401c8, 1, 0xf, 0xfff},
	{ 0x140200, 6, 0xf, 0xfff},
	{ 0x140338, 7, 0x10, 0xfff},
	{ 0x140370, 7, 0x10, 0xfff},
	{ 0x15c1bc, 6, 0x10, 0xfff},
	{ 0x15c230, 7, 0x10, 0xfff},
	{ 0x16101c, 1, 0x1f, 0xfff},
	{ 0x16102c, 1, 0x1f, 0x1fff},
	{ 0x164014, 2, 0x1f, 0xfff},
	{ 0x1640f0, 1, 0x1f, 0xfff},
	{ 0x166290, 1, 0x1f, 0xfff},
	{ 0x1662a0, 1, 0x1f, 0xfff},
	{ 0x1662b0, 1, 0x1f, 0x1fff},
	{ 0x166548, 1, 0x1f, 0xfff},
	{ 0x166550, 1, 0x1f, 0xfff},
	{ 0x166558, 1, 0x1f, 0xfff},
	{ 0x168000, 1, 0x1f, 0xfff},
	{ 0x168008, 1, 0x1f, 0xfff},
	{ 0x168010, 1, 0x1f, 0xfff},
	{ 0x168018, 1, 0x1f, 0xfff},
	{ 0x168028, 2, 0x1f, 0xfff},
	{ 0x168058, 9, 0x1f, 0xfff},
	{ 0x168238, 1, 0x1f, 0xfff},
	{ 0x1682d0, 7, 0x1f, 0xfff},
	{ 0x168300, 2, 0x3, 0xfff},
	{ 0x168308, 65, 0x1f, 0xfff},
	{ 0x168410, 2, 0x1f, 0xfff},
	{ 0x168438, 1, 0x1f, 0xfff},
	{ 0x168448, 1, 0x1f, 0x1fff},
	{ 0x168a00, 128, 0x1f, 0xfff},
	{ 0x16e200, 128, 0x2, 0xfff},
	{ 0x16e404, 2, 0x2, 0xfff},
	{ 0x16e584, 64, 0x2, 0xfff},
	{ 0x16e684, 2, 0x1e, 0xfff},
	{ 0x16e68c, 4, 0x2, 0xfff},
	{ 0x16e6fc, 4, 0x1c, 0xfff},
	{ 0x16e7ac, 12, 0x10, 0xfff},
	{ 0x1700a4, 1, 0x1f, 0xfff},
	{ 0x1700ac, 2, 0x1f, 0xfff},
	{ 0x1700c0, 1, 0x1f, 0xfff},
	{ 0x170174, 1, 0x1f, 0xfff},
	{ 0x170184, 1, 0x1f, 0x1fff},
	{ 0x1800f4, 1, 0x1f, 0xfff},
	{ 0x180104, 1, 0x1f, 0xfff},
	{ 0x180114, 1, 0x1f, 0x1fff},
	{ 0x180124, 1, 0x1f, 0x1fff},
	{ 0x18026c, 1, 0x1f, 0xfff},
	{ 0x1802a0, 1, 0x1f, 0xfff},
	{ 0x1b8000, 1, 0x1f, 0xfff},
	{ 0x1b8040, 1, 0x1f, 0xfff},
	{ 0x1b8080, 1, 0x1f, 0xfff},
	{ 0x1b80c0, 1, 0x1f, 0xfff},
	{ 0x200104, 1, 0x1f, 0xfff},
	{ 0x200114, 1, 0x1f, 0xfff},
	{ 0x200124, 1, 0x1f, 0x1fff},
	{ 0x200134, 1, 0x1f, 0x1fff},
	{ 0x20026c, 1, 0x1f, 0xfff},
	{ 0x2002a0, 1, 0x1f, 0xfff},
	{ 0x238000, 1, 0x1f, 0xfff},
	{ 0x238040, 1, 0x1f, 0xfff},
	{ 0x238080, 1, 0x1f, 0xfff},
	{ 0x2380c0, 1, 0x1f, 0xfff},
	{ 0x280104, 1, 0x1f, 0xfff},
	{ 0x280114, 1, 0x1f, 0xfff},
	{ 0x280124, 1, 0x1f, 0x1fff},
	{ 0x280134, 1, 0x1f, 0x1fff},
	{ 0x28026c, 1, 0x1f, 0xfff},
	{ 0x2802a0, 1, 0x1f, 0xfff},
	{ 0x2b8000, 1, 0x1f, 0xfff},
	{ 0x2b8040, 1, 0x1f, 0xfff},
	{ 0x2b8080, 1, 0x1f, 0xfff},
	{ 0x300104, 1, 0x1f, 0xfff},
	{ 0x300114, 1, 0x1f, 0xfff},
	{ 0x300124, 1, 0x1f, 0x1fff},
	{ 0x300134, 1, 0x1f, 0x1fff},
	{ 0x30026c, 1, 0x1f, 0xfff},
	{ 0x3002a0, 1, 0x1f, 0xfff},
	{ 0x338000, 1, 0x1f, 0xfff},
	{ 0x338040, 1, 0x1f, 0xfff},
	{ 0x338080, 1, 0x1f, 0xfff},
	{ 0x3380c0, 1, 0x1f, 0xfff}
};

#define IDLE_REGS_COUNT ARRAY_SIZE(idle_reg_addrs)

static const u32 read_reg_e1[] = {
	0x1b1000};

static const struct wreg_addr wreg_addr_e1 = {
	0x1b0c00, 192, 1, read_reg_e1, 0x1f, 0x1fff};

static const u32 read_reg_e1h[] = {
	0x1b1040, 0x1b1000};

static const struct wreg_addr wreg_addr_e1h = {
	0x1b0c00, 256, 2, read_reg_e1h, 0x1f, 0x1fff};

static const u32 read_reg_e2[] = {
	0x1b1040, 0x1b1000};

static const struct wreg_addr wreg_addr_e2 = {
	0x1b0c00, 128, 2, read_reg_e2, 0x1f, 0x1fff};

static const u32 read_reg_e3[] = {
	0x1b1040, 0x1b1000};

static const struct wreg_addr wreg_addr_e3 = {
	0x1b0c00, 128, 2, read_reg_e3, 0x1f, 0x1fff};

static const u32 read_reg_e3b0[] = {
	0x1b1040, 0x1b1000};

static const struct wreg_addr wreg_addr_e3b0 = {
	0x1b0c00, 128, 2, read_reg_e3b0, 0x1f, 0x1fff};

static const unsigned int dump_num_registers[NUM_CHIPS][NUM_PRESETS] = {
	{19758, 17543, 26951, 18705, 17287, 26695, 19812, 31367, 40775, 19788,
	 25223, 34631, 19074},
	{31750, 18273, 32253, 30697, 18017, 31997, 31804, 32097, 46077, 31780,
	 25953, 39933, 35895},
	{36527, 17928, 33697, 35474, 18700, 34466, 36581, 31752, 47521, 36557,
	 25608, 41377, 43903},
	{45239, 17936, 34387, 44186, 18708, 35156, 45293, 31760, 48211, 45269,
	 25616, 42067, 43903},
	{45302, 17999, 34802, 44249, 18771, 35571, 45356, 31823, 48626, 45332,
	 25679, 42482, 43903}
};
#endif
