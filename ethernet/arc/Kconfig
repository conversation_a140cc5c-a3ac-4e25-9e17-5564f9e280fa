# SPDX-License-Identifier: GPL-2.0-only
#
# ARC EMAC network device configuration
#

config NET_VENDOR_ARC
	bool "ARC devices"
	default y
	---help---
	  If you have a network (Ethernet) card belonging to this class, say Y.

	  Note that the answer to this question doesn't directly affect the
	  kernel: saying N will just cause the configurator to skip all
	  the questions about ARC cards. If you say Y, you will be asked for
	  your specific card in the following questions.

if NET_VENDOR_ARC

config ARC_EMAC_CORE
	tristate
	depends on ARC || ARCH_ROCKCHIP || COMPILE_TEST
	select MII
	select PHY<PERSON><PERSON>
	select CRC32

config ARC_EMAC
	tristate "ARC EMAC support"
	select ARC_EMAC_CORE
	depends on OF_IRQ && OF_NET
	depends on ARC || COMPILE_TEST
	---help---
	  On some legacy ARC (Synopsys) FPGA boards such as ARCAngel4/ML50x
	  non-standard on-chip ethernet device ARC EMAC 10/100 is used.
	  Say Y here if you have such a board.  If unsure, say N.

config EMAC_ROCKCHIP
	tristate "Rockchip EMAC support"
	select ARC_EMAC_CORE
	depends on OF_IRQ && OF_NET && REGULATOR
	depends on ARCH_ROCKCHIP || COMPILE_TEST
	---help---
	  Support for Rockchip RK3036/RK3066/RK3188 EMAC ethernet controllers.
	  This selects Rockchip SoC glue layer support for the
	  emac device driver. This driver is used for RK3036/RK3066/RK3188
	  EMAC ethernet controller.

endif # NET_VENDOR_ARC
