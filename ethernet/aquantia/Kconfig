# SPDX-License-Identifier: GPL-2.0-only
#
# aQuantia device configuration
#

config NET_VENDOR_AQUANTIA
	bool "aQuantia devices"
	default y
	---help---
	  Set this to y if you have an Ethernet network cards that uses the aQuantia
	  AQC107/AQC108 chipset.

	  This option does not build any drivers; it causes the aQuantia
	  drivers that can be built to appear in the list of Ethernet drivers.


if NET_VENDOR_AQUANTIA

config AQTION
	tristate "aQuantia AQtion(tm) Support"
	depends on PCI
	depends on X86_64 || ARM64 || COMPILE_TEST
	---help---
	  This enables the support for the aQuantia AQtion(tm) Ethernet card.

endif # NET_VENDOR_AQUANTIA
